# Análisis de la Subtarea TSFD-7941

## Descripción

La subtarea TSFD-7941 se centra en la implementación de dos endpoints POST para crear ajustes. Esta tarea involucra definir la estructura del payload, implementar la lógica de negocio, crear los endpoints en el controlador y realizar pruebas unitarias.

## Checklist

- [x] Definir estructura del payload para el endpoint post create adjustmentType
- [x] Implementar lógica de negocio para el endpoint post create adjustmentType
- [x] Crear endpoint en el controlador para el endpoint post create adjustmentType
- [x] Realizar pruebas unitarias para el endpoint post create adjustmentType
- [x] Definir estructura del payload para el endpoint post create debitAdjustmentType
- [x] Implementar lógica de negocio para el endpoint post create debitAdjustmentType
- [x] Crear endpoint en el controlador para el endpoint post create debitAdjustmentType
- [x] Realizar pruebas unitarias para el endpoint post create debitAdjustmentType

## Detalles de Implementación

Se han implementado los siguientes cambios:

- **Definición del payload**: Se ha definido la estructura del payload para el endpoint POST en `AdjustmentRequest.java`.
- **Lógica de negocio**: Se ha implementado la lógica de negocio en `AdjustmentServiceImpl.java`.
- **Endpoint en el controlador**: Se ha creado el endpoint POST en `AdjustmentController.java`.
- **Pruebas unitarias**: Se han realizado pruebas unitarias en `AdjustmentServiceImplTest.java` y `AdjustmentControllerTest.java`.

- **Definición del payload para debitAdjustmentType**: Se ha definido la estructura del payload para el endpoint POST en `DebitAdjustmentTypeRequest.java`.
- **Lógica de negocio para debitAdjustmentType**: Se ha implementado la lógica de negocio en `DebitAdjustmentTypeServiceImpl.java`.
- **Endpoint en el controlador para debitAdjustmentType**: Se ha creado el endpoint POST en `DebitAdjustmentTypeController.java`.
- **Pruebas unitarias para debitAdjustmentType**: Se han realizado pruebas unitarias en `DebitAdjustmentTypeControllerTest.java`.


## Conclusión

El commit TSFD-7941 cumple con lo solicitado en la subtarea TSFD-7941. Todas las tareas del checklist han sido completadas excepto las del segundo endpoint que aun estan pendientes.
