global:
  name: adjustment-custom-service-co
  description: Adjustments Custom Service
  maintainers:
    - email: <EMAIL>
      name: <PERSON>
  labels:
    team: risf

main:
  replicas: 1
  securityContext: disabled
  image:
    pullPolicy: Always
    repository: nexus-tsf.tigo.cam/galaxion/glx-co/backend/adjustment-custom-service
    tag: develop
  port: 8080
  alivePath: /actuator/health
  readyPath: /actuator/health
  labels:
    owner: risf
  resources:
    limits:
      cpu: "500m"
      memory: "1Gi"
    requests:
      cpu: "500m"
      memory: "1Gi"

configuration:
  env:
    SPRING_DATASOURCE_URL: ***************************,************:3306/adjustment_custom
