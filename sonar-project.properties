# Required metadata
sonar.projectKey=galaxion_projects_millicom_backend_adjustment-custom-service
sonar.projectName=adjustment-custom-service
sonar.scm.provider=git
sonar.sources=src
sonar.language=java
sonar.java.binaries=target
sonar.java.source=21
sonar.sourceEncoding=UTF-8

sonar.exclusions=src/test/**/*,\
                 target,\
                 src/main/resources/**,\
                 src/main/java/com/millicom/adjustment_custom_service/config/**,\
                 src/main/java/com/millicom/adjustment_custom_service/domain/**,\
                 src/main/java/com/millicom/adjustment_custom_service/exception/**,\
                 src/main/java/com/millicom/adjustment_custom_service/AdjustmentCustomServiceApplication.java

# Report Dependency Check
sonar.dependencyCheck.jsonReportPath=dependency-check-report.json
sonar.dependencyCheck.htmlReportPath=dependency-check-report.html

