package com.millicom.adjustment_custom_service.controller;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentRequest;
import com.millicom.adjustment_custom_service.controller.request.UpdateAdjustmentRequest;
import com.millicom.adjustment_custom_service.controller.request.UpdateAdjustmentDetailRequest;
import com.millicom.adjustment_custom_service.controller.response.NewAdjustmentResponseDTO;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentDetailDTO;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentResponseDTO;
import com.millicom.adjustment_custom_service.domain.entity.Adjustment;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentDetail;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentDetailId;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;
import com.millicom.adjustment_custom_service.service.AdjustmentService;
import com.millicom.adjustment_custom_service.exception.custom.ResourceNotFoundException;
import com.millicom.adjustment_custom_service.exception.custom.BadRequestException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class AdjustmentControllerTest {

    @Mock
    private AdjustmentService adjustmentService;

    @InjectMocks
    private AdjustmentController adjustmentController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // ----------------- CREATE -----------------

    @Test
    void testCreateAdjustment_Success() {
        AdjustmentRequest adjustmentRequest = new AdjustmentRequest();

        Adjustment adjustment = new Adjustment();
        adjustment.setAdjustmentId(1L);
        adjustment.setInvoiceId(123L); // evitar problemas de tipos

        when(adjustmentService.createAdjustment(any(AdjustmentRequest.class))).thenReturn(adjustment);

        ResponseEntity<NewAdjustmentResponseDTO> response = adjustmentController.createAdjustment(adjustmentRequest);

        assertNotNull(response);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertEquals(1L, response.getBody().getAdjustmentId());
        verify(adjustmentService, times(1)).createAdjustment(any(AdjustmentRequest.class));
    }

    @Test
    void testCreateAdjustment_ServiceThrowsException() {
        AdjustmentRequest adjustmentRequest = new AdjustmentRequest();
        when(adjustmentService.createAdjustment(any(AdjustmentRequest.class)))
                .thenThrow(new RuntimeException("Service error"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            adjustmentController.createAdjustment(adjustmentRequest);
        });

        assertEquals("Service error", exception.getMessage());
        verify(adjustmentService, times(1)).createAdjustment(any(AdjustmentRequest.class));
    }

    // ----------------- UPDATE -----------------

    @Test
    void testUpdateAdjustment_Success() {
        Long adjustmentId = 1L;
        Long idRubro = 133L;

        // El controller exige que si viene idRubro, venga un (1) detalle
        UpdateAdjustmentRequest updateAdjustmentRequest = new UpdateAdjustmentRequest();
        updateAdjustmentRequest.setDetail(List.of(new UpdateAdjustmentDetailRequest()));

        Adjustment updatedAdjustment = new Adjustment();
        updatedAdjustment.setAdjustmentId(adjustmentId);
        updatedAdjustment.setInvoiceId(123L);

        when(adjustmentService.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest))
                .thenReturn(updatedAdjustment);

        ResponseEntity<NewAdjustmentResponseDTO> response =
                adjustmentController.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);

        assertNotNull(response);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertEquals(adjustmentId, response.getBody().getAdjustmentId());

        verify(adjustmentService, times(1)).updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);
    }

    @Test
    void testUpdateAdjustment_NotFound() {
        Long adjustmentId = 1L;
        Long idRubro = 133L;

        // Para que no truene por validación del controller
        UpdateAdjustmentRequest updateAdjustmentRequest = new UpdateAdjustmentRequest();
        updateAdjustmentRequest.setDetail(List.of(new UpdateAdjustmentDetailRequest()));

        when(adjustmentService.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest))
                .thenThrow(new ResourceNotFoundException("Adjustment not found"));

        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
            adjustmentController.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);
        });

        assertEquals("Adjustment not found", exception.getMessage());
        verify(adjustmentService, times(1)).updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);
    }

    @Test
    void testUpdateAdjustment_InvalidInput_MissingAdjustmentDetail() {
        Long adjustmentId = 1L;
        Long idRubro = 133L;
        UpdateAdjustmentRequest updateAdjustmentRequest = new UpdateAdjustmentRequest();

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            adjustmentController.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);
        });

        assertEquals("El campo adjustmentDetail es requerido cuando idRubro está presente.", exception.getMessage());
        verify(adjustmentService, never()).updateAdjustment(any(), any(), any());
    }

    @Test
    void testUpdateAdjustment_InvalidInput_MultipleAdjustmentDetails() {
        Long adjustmentId = 1L;
        Long idRubro = 133L;
        UpdateAdjustmentRequest updateAdjustmentRequest = new UpdateAdjustmentRequest();
        updateAdjustmentRequest.setDetail(List.of(new UpdateAdjustmentDetailRequest(), new UpdateAdjustmentDetailRequest()));

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            adjustmentController.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);
        });

        assertEquals("Solo se puede definir un elemento en adjustmentDetail.", exception.getMessage());
        verify(adjustmentService, never()).updateAdjustment(any(), any(), any());
    }

    @Test
    void testUpdateAdjustment_InvalidInput_MissingIdRubroInQueryParam() {
        Long adjustmentId = 1L;
        UpdateAdjustmentRequest updateAdjustmentRequest = new UpdateAdjustmentRequest();
        UpdateAdjustmentDetailRequest detail = new UpdateAdjustmentDetailRequest();
        updateAdjustmentRequest.setDetail(List.of(detail));

        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            adjustmentController.updateAdjustment(adjustmentId, null, updateAdjustmentRequest);
        });

        assertEquals(
                "El campo idRubro es requerido en el query param cuando adjustmentDetail está presente en el body.",
                exception.getMessage());
        verify(adjustmentService, never()).updateAdjustment(any(), any(), any());
    }

    @Test
    void testUpdateAdjustment_ServiceThrowsException() {
        Long adjustmentId = 1L;
        Long idRubro = 133L;
        UpdateAdjustmentRequest updateAdjustmentRequest = new UpdateAdjustmentRequest();
        UpdateAdjustmentDetailRequest detail = new UpdateAdjustmentDetailRequest();
        updateAdjustmentRequest.setDetail(List.of(detail));

        when(adjustmentService.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest))
                .thenThrow(new RuntimeException("Service error"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            adjustmentController.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);
        });

        assertEquals("Service error", exception.getMessage());
        verify(adjustmentService, times(1)).updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);
    }

    // ----------------- GET -----------------

    @Test
    void getAdjustment_shouldReturn400_whenAllKeyParamsAreNull() {
        BadRequestException ex = assertThrows(
                BadRequestException.class,
                () -> adjustmentController.getAdjustment(null, null, null, null));
        assertTrue(ex.getMessage().contains("al menos 'adjustmentId', 'invoiceId' o 'caseId'"));
        verifyNoInteractions(adjustmentService);
    }

    @Test
    void getAdjustment_shouldReturn204_whenServiceReturnsEmpty() {
        Long adjId = 10L;
        when(adjustmentService.findAdjustment(null, null, null, adjId))
                .thenReturn(Optional.empty());

        ResponseEntity<AdjustmentResponseDTO> resp = adjustmentController.getAdjustment(adjId, null, null, null);

        assertEquals(204, resp.getStatusCode().value());
        assertNull(resp.getBody());
        verify(adjustmentService, times(1)).findAdjustment(null, null, null, adjId);
    }

    @Test
    void getAdjustment_shouldReturn200_andMapDTO_whenAdjustmentFound() {
        Long adjustmentId = 99L;
        Long invoiceId = 12345L;
        Long caseId = 123L;
        String status = "init";

        Adjustment adj = mock(Adjustment.class);
        when(adj.getAdjustmentId()).thenReturn(adjustmentId);
        when(adj.getInvoiceId()).thenReturn(invoiceId);
        when(adj.getAccountId()).thenReturn("ACC-1");
        when(adj.getTotalAmount()).thenReturn(new BigDecimal("121.00"));
        when(adj.getCreatedBy()).thenReturn("tester");
        when(adj.getCaseId()).thenReturn(caseId);

        AdjustmentType type = new AdjustmentType();
        type.setId(7L);
        type.setBillingCode("C_ADJ_FIXED");
        type.setType(AdjustmentType.AdjustmentKind.CREDIT); // evita NPE
        when(adj.getAdjustmentTypeId()).thenReturn(type);

        AdjustmentDetailId detId1 = new AdjustmentDetailId();
        detId1.setAdjustmentId(adjustmentId);
        detId1.setRubroId(1L);
        AdjustmentDetail det1 = new AdjustmentDetail();
        det1.setId(detId1);
        det1.setAdjustmentAmount(new BigDecimal("50.00"));
        det1.setStatus("init");
        det1.setDisputeSerialNo("DSP-1");
        det1.setAdjustmentSerialNo("ADJ-1");

        AdjustmentDetailId detId2 = new AdjustmentDetailId();
        detId2.setAdjustmentId(adjustmentId);
        detId2.setRubroId(2L);
        AdjustmentDetail det2 = new AdjustmentDetail();
        det2.setId(detId2);
        det2.setAdjustmentAmount(new BigDecimal("71.00"));
        det2.setStatus("init");
        det2.setDisputeSerialNo("DSP-2");
        det2.setAdjustmentSerialNo("ADJ-2");

        when(adj.getDetail()).thenReturn(List.of(det1, det2));
        when(adjustmentService.findAdjustment(invoiceId, caseId, status, adjustmentId))
                .thenReturn(Optional.of(adj));

        ResponseEntity<AdjustmentResponseDTO> resp =
                adjustmentController.getAdjustment(adjustmentId, invoiceId, caseId, status);

        assertEquals(200, resp.getStatusCode().value());
        AdjustmentResponseDTO body = resp.getBody();
        assertNotNull(body);

        assertEquals(adjustmentId, body.getId());
        assertEquals(invoiceId, body.getInvoiceId());
        assertEquals("ACC-1", body.getAccountId());
        assertEquals(new BigDecimal("121.00"), body.getTotalAmount());
        assertEquals("tester", body.getCreatedBy());
        assertEquals(caseId, body.getCaseId());
        assertEquals(7L, body.getAdjustmentTypeId());
        assertEquals("C_ADJ_FIXED", body.getChargeCode());

        assertEquals("DSP-1", body.getDisputeSerialNo());

        assertNotNull(body.getDetail());
        assertEquals(2, body.getDetail().size());

        AdjustmentDetailDTO d1 = body.getDetail().get(0);
        assertEquals(1L, d1.getIdRubro());
        assertEquals(new BigDecimal("50.00"), d1.getAmount());
        assertEquals("init", d1.getStatus());
        assertEquals("DSP-1", d1.getDisputeSerialNo());
        assertEquals("ADJ-1", d1.getAdjustmentSerialNo());

        AdjustmentDetailDTO d2 = body.getDetail().get(1);
        assertEquals(2L, d2.getIdRubro());
        assertEquals(new BigDecimal("71.00"), d2.getAmount());
        assertEquals("init", d2.getStatus());
        assertEquals("DSP-2", d2.getDisputeSerialNo());
        assertEquals("ADJ-2", d2.getAdjustmentSerialNo());

        assertEquals("20370101000000", body.getDisputeReason());

        verify(adjustmentService, times(1))
                .findAdjustment(invoiceId, caseId, status, adjustmentId);
    }

    @Test
    void getAdjustment_shouldDelegateParametersCorrectly_whenOnlyInvoiceIsProvided() {
        when(adjustmentService.findAdjustment(333L, null, null, null))
                .thenReturn(Optional.empty());

        ResponseEntity<AdjustmentResponseDTO> resp = adjustmentController.getAdjustment(null, 333L, null, null);

        assertEquals(204, resp.getStatusCode().value());
        verify(adjustmentService, times(1))
                .findAdjustment(333L, null, null, null);
    }

    @Test
    void getAdjustment_shouldDelegateParametersCorrectly_whenOnlyCaseIsProvided() {
        when(adjustmentService.findAdjustment(null, 77L, "pending", null))
                .thenReturn(Optional.empty());

        ResponseEntity<AdjustmentResponseDTO> resp = adjustmentController.getAdjustment(null, null, 77L, "pending");

        assertEquals(204, resp.getStatusCode().value());
        verify(adjustmentService, times(1))
                .findAdjustment(null, 77L, "pending", null);
    }
}
