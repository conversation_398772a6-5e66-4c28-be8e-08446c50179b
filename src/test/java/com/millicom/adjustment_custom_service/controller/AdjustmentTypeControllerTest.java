package com.millicom.adjustment_custom_service.controller;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentTypeRequest;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeCatalogResponse;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeCreateResponse;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeResponse;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;
import com.millicom.adjustment_custom_service.service.AdjustmentTypeService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class AdjustmentTypeControllerTest {

    @Mock
    private AdjustmentTypeService adjustmentTypeService;

    @InjectMocks
    private AdjustmentTypeController adjustmentTypeController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Simular una petición HTTP para que funcione ServletUriComponentsBuilder.fromCurrentRequest()
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setMethod("POST");
        request.setRequestURI("/api/v1/adjustment-types");
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
    }

    @AfterEach
    void tearDown() {
        // Evitar que quede “sucio” el contexto entre tests
        RequestContextHolder.resetRequestAttributes();
    }

    @Test
    void testCreateAdjustmentType_Success() {
        // Arrange
        AdjustmentTypeRequest request = new AdjustmentTypeRequest();
        request.setDescription("Internet reconnection");
        request.setBillingCode("CC_ADJ_001");
        request.setType(AdjustmentType.AdjustmentKind.CREDIT);

        AdjustmentType createdEntity = new AdjustmentType();
        createdEntity.setId(123L);
        createdEntity.setDescription("Internet reconnection");
        createdEntity.setBillingCode("CC_ADJ_001");
        createdEntity.setType(AdjustmentType.AdjustmentKind.CREDIT);

        when(adjustmentTypeService.createAdjustmentType(any(AdjustmentTypeRequest.class)))
                .thenReturn(createdEntity);

        // Act
        ResponseEntity<AdjustmentTypeCreateResponse> response = adjustmentTypeController.createAdjustmentType(request);

        // Assert
        assertNotNull(response);
        assertEquals(201, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(123L, response.getBody().id());
        verify(adjustmentTypeService, times(1)).createAdjustmentType(any(AdjustmentTypeRequest.class));
    }

    @Test
    void testListAdjustmentTypes_WithData() {
        // Arrange
        AdjustmentTypeResponse response1 = new AdjustmentTypeResponse(
                1L, "Sample credit", "CC_ADJ_001", AdjustmentType.AdjustmentKind.CREDIT
        );
        AdjustmentTypeResponse response2 = new AdjustmentTypeResponse(
                2L, "Sample debit", "CC_ADJ_002", AdjustmentType.AdjustmentKind.DEBIT
        );

        when(adjustmentTypeService.listAdjustmentTypes()).thenReturn(List.of(response1, response2));

        // Act
        ResponseEntity<AdjustmentTypeCatalogResponse> response = adjustmentTypeController.listAdjustmentType();

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().getCatalog().size());
        assertEquals(1L, response.getBody().getCatalog().get(0).getId());
        assertEquals(2L, response.getBody().getCatalog().get(1).getId());
        verify(adjustmentTypeService, times(1)).listAdjustmentTypes();
    }

    @Test
    void testListAdjustmentTypes_NoContent() {
        // Arrange
        when(adjustmentTypeService.listAdjustmentTypes()).thenReturn(List.of());

        // Act
        ResponseEntity<AdjustmentTypeCatalogResponse> response = adjustmentTypeController.listAdjustmentType();

        // Assert
        assertNotNull(response);
        assertEquals(204, response.getStatusCodeValue());
        assertNull(response.getBody());
        verify(adjustmentTypeService, times(1)).listAdjustmentTypes();
    }
}
