package com.millicom.adjustment_custom_service.util;

import com.millicom.adjustment_custom_service.domain.entity.Adjustment;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static com.millicom.adjustment_custom_service.util.AdjustmentUtils.*;
import static org.junit.jupiter.api.Assertions.*;

class AdjustmentUtilsTest {

    // Helper para crear un Adjustment mínimo para las pruebas
    private Adjustment newAdj(Long id, Long invoiceId, Long caseId) {
        Adjustment a = new Adjustment();
        a.setAdjustmentId(id);
        a.setInvoiceId(invoiceId);
        a.setCaseId(caseId);
        return a;
    }

    // -------- normalizeStatus --------

    @Test
    @DisplayName("normalizeStatus: null -> null")
    void normalizeStatus_null() {
        assertNull(normalizeStatus(null));
    }

    @Test
    @DisplayName("normalizeStatus: blank -> null")
    void normalizeStatus_blank() {
        assertNull(normalizeStatus(""));
        assertNull(normalizeStatus("   "));
    }

    @Test
    @DisplayName("normalizeStatus: 'all' (cualquier case) -> null")
    void normalizeStatus_all() {
        assertNull(normalizeStatus("all"));
        assertNull(normalizeStatus("ALL"));
        assertNull(normalizeStatus("AlL"));
    }

    @Test
    @DisplayName("normalizeStatus: otro valor se conserva")
    void normalizeStatus_other() {
        assertEquals("init", normalizeStatus("init"));
        assertEquals("pending", normalizeStatus("pending"));
    }

    // -------- firstOrEmpty --------

    @Test
    @DisplayName("firstOrEmpty: lista vacía -> Optional.empty()")
    void firstOrEmpty_empty() {
        assertTrue(firstOrEmpty(List.of()).isEmpty());
    }

    @Test
    @DisplayName("firstOrEmpty: lista con elementos -> devuelve el primero")
    void firstOrEmpty_first() {
        Adjustment a1 = newAdj(1L, 333L, 10L);
        Adjustment a2 = newAdj(2L, 333L, 20L);
        Optional<Adjustment> out = firstOrEmpty(List.of(a1, a2));

        assertTrue(out.isPresent());
        assertEquals(1L, out.get().getAdjustmentId());
    }

    // -------- validateAgainst --------

    @Test
    @DisplayName("validateAgainst: Optional.empty() -> Optional.empty()")
    void validateAgainst_emptyOptional() {
        assertTrue(validateAgainst(Optional.empty(), null, null).isEmpty());
    }

    @Test
    @DisplayName("validateAgainst: sin filtros -> devuelve el mismo Optional")
    void validateAgainst_noFilters() {
        Adjustment a = newAdj(5L, 333L, 100L);
        Optional<Adjustment> out = validateAgainst(Optional.of(a), null, null);

        assertTrue(out.isPresent());
        assertSame(a, out.get());
    }

    @Test
    @DisplayName("validateAgainst: invoiceId coincide -> OK")
    void validateAgainst_invoice_match() {
        Adjustment a = newAdj(6L, 333L, 100L);
        Optional<Adjustment> out = validateAgainst(Optional.of(a), 333L, null);

        assertTrue(out.isPresent());
    }

    @Test
    @DisplayName("validateAgainst: invoiceId NO coincide -> Optional.empty()")
    void validateAgainst_invoice_mismatch() {
        Adjustment a = newAdj(7L, 333L, 100L);
        // << antes pasabas 333L (coincidía); ahora usamos 999L para forzar el mismatch
        Optional<Adjustment> out = validateAgainst(Optional.of(a), 999L, null);

        assertTrue(out.isEmpty());
    }

    @Test
    @DisplayName("validateAgainst: caseId coincide -> OK")
    void validateAgainst_case_match() {
        Adjustment a = newAdj(8L, 333L, 200L);
        Optional<Adjustment> out = validateAgainst(Optional.of(a), null, 200L);

        assertTrue(out.isPresent());
    }

    @Test
    @DisplayName("validateAgainst: caseId NO coincide -> Optional.empty()")
    void validateAgainst_case_mismatch() {
        Adjustment a = newAdj(9L, 333L, 200L);
        Optional<Adjustment> out = validateAgainst(Optional.of(a), null, 201L);

        assertTrue(out.isEmpty());
    }

    @Test
    @DisplayName("validateAgainst: caseId en entidad es null y filtro no-null -> Optional.empty()")
    void validateAgainst_case_entityNull_filterNotNull() {
        Adjustment a = newAdj(10L, 333L, null);
        Optional<Adjustment> out = validateAgainst(Optional.of(a), null, 1L);

        assertTrue(out.isEmpty());
    }

    @Test
    @DisplayName("validateAgainst: invoice y case coinciden -> OK")
    void validateAgainst_both_match() {
        Adjustment a = newAdj(11L, 333L, 777L);
        Optional<Adjustment> out = validateAgainst(Optional.of(a), 333L, 777L);

        assertTrue(out.isPresent());
        assertEquals(11L, out.get().getAdjustmentId());
    }
}
