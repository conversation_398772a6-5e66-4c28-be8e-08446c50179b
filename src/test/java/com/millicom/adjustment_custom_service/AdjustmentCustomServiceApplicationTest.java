package com.millicom.adjustment_custom_service;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import org.springframework.context.ApplicationContext;

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.NONE,
    properties = {
        // Deshabilita Liquibase para que no intente migrar H2 durante tests de contexto
        "spring.liquibase.enabled=false",
        // Opcionalmente evita que JPA toque el esquema
        "spring.jpa.hibernate.ddl-auto=none"
    }
)
@ActiveProfiles("test")
class AdjustmentCustomServiceApplicationTest {

    private static final Logger logger = LoggerFactory.getLogger(AdjustmentCustomServiceApplicationTest.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void contextLoads() {
        logger.info("AdjustmentCustomServiceApplicationTest is running");
        // Smoke test: solo valida que el contexto levanta
        org.assertj.core.api.Assertions.assertThat(applicationContext).isNotNull();
    }
}
