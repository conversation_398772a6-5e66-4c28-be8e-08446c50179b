package com.millicom.adjustment_custom_service.service.impl;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentRequest;
import com.millicom.adjustment_custom_service.controller.request.UpdateAdjustmentRequest;
import com.millicom.adjustment_custom_service.domain.entity.Adjustment;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;
import com.millicom.adjustment_custom_service.domain.repository.AdjustmentDetailRepository;
import com.millicom.adjustment_custom_service.domain.repository.AdjustmentRepository;
import com.millicom.adjustment_custom_service.domain.repository.AdjustmentTypeRepository;
import com.millicom.adjustment_custom_service.util.ResourceLoader;
import com.millicom.adjustment_custom_service.exception.custom.ResourceNotFoundException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.dao.DataIntegrityViolationException;

import java.io.IOException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.DisplayName;

class AdjustmentServiceImplTest {
        private final ResourceLoader resourceLoader = new ResourceLoader();

        @Mock
        private AdjustmentRepository adjustmentRepository;

        @Mock
        private AdjustmentDetailRepository adjustmentDetailRepository;

        @Mock
        private AdjustmentTypeRepository debitAdjustmentTypeRepository;

        @InjectMocks
        private AdjustmentServiceImpl adjustmentService;

        private Adjustment newAdj(Long id, Long invoiceId, Long caseId) {
                Adjustment a = new Adjustment();
                a.setAdjustmentId(id);
                a.setInvoiceId(invoiceId);
                a.setCaseId(caseId);
                return a;
        }

        @BeforeEach
        void setUp() {
                MockitoAnnotations.openMocks(this);
        }

        @Test
        void testCreateAdjustment_Success() throws IOException {
                AdjustmentRequest adjustmentRequest = resourceLoader.loadResource(
                                "json/adjustment/testCreateAdjustment_Success.json",
                                AdjustmentRequest.class);

                // Evitar NPE en service cuando recorre detail
                adjustmentRequest.setDetail(java.util.Collections.emptyList());

                AdjustmentType debitAdjustmentType = new AdjustmentType();
                debitAdjustmentType.setId(adjustmentRequest.getAdjustmentTypeId());

                Adjustment saved = new Adjustment();
                saved.setAdjustmentId(1L);

                when(debitAdjustmentTypeRepository.findById(adjustmentRequest.getAdjustmentTypeId()))
                                .thenReturn(java.util.Optional.of(debitAdjustmentType));
                when(adjustmentRepository.save(any(Adjustment.class))).thenReturn(saved);

                Adjustment result = adjustmentService.createAdjustment(adjustmentRequest);

                assertNotNull(result);
                assertEquals(1L, result.getAdjustmentId());
                verify(adjustmentRepository, times(1)).save(any(Adjustment.class));
                verify(adjustmentDetailRepository, times(1)).saveAll(anyList());
        }

        @Test
        void testCreateAdjustment_InvalidTypeDebitId() throws IOException {
                AdjustmentRequest adjustmentRequest = resourceLoader.loadResource(
                                "json/adjustment/testCreateAdjustment_InvalidTypeDebitId.json",
                                AdjustmentRequest.class);

                // También evitar NPE si el servicio recorre detail antes de fallar
                adjustmentRequest.setDetail(java.util.Collections.emptyList());

                when(debitAdjustmentTypeRepository.findById(adjustmentRequest.getAdjustmentTypeId()))
                                .thenReturn(Optional.empty());

                assertThrows(DataIntegrityViolationException.class, () -> {
                        adjustmentService.createAdjustment(adjustmentRequest);
                });

                verify(adjustmentRepository, never()).save(any(Adjustment.class));
                verify(adjustmentDetailRepository, never()).saveAll(anyList());
        }

        @Test
        void testUpdateAdjustment_Success() throws IOException {
                Long adjustmentId = 1L;
                Long idRubro = 1L;

                UpdateAdjustmentRequest updateAdjustmentRequest = resourceLoader
                                .loadResource("json/adjustment/UpdateAdjustmentRequest.json",
                                                UpdateAdjustmentRequest.class);

                // Entidad existente mínima con 2 detalles
                Adjustment existingAdjustment = new Adjustment();
                existingAdjustment.setAdjustmentId(adjustmentId);

                var d1id = new com.millicom.adjustment_custom_service.domain.entity.AdjustmentDetailId();
                d1id.setAdjustmentId(adjustmentId);
                d1id.setRubroId(idRubro);
                var d1 = new com.millicom.adjustment_custom_service.domain.entity.AdjustmentDetail();
                d1.setId(d1id);

                var d2id = new com.millicom.adjustment_custom_service.domain.entity.AdjustmentDetailId();
                d2id.setAdjustmentId(adjustmentId);
                d2id.setRubroId(2L);
                var d2 = new com.millicom.adjustment_custom_service.domain.entity.AdjustmentDetail();
                d2.setId(d2id);

                existingAdjustment.setDetail(java.util.List.of(d1, d2));

                when(adjustmentRepository.findByAdjustmentIdAndRubroId(adjustmentId, idRubro))
                                .thenReturn(java.util.Optional.of(existingAdjustment));

                // devolvemos exactamente lo que entra a save
                when(adjustmentRepository.save(any(Adjustment.class)))
                                .thenAnswer(inv -> inv.getArgument(0));

                // Act
                Adjustment result = adjustmentService.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);

                // Assert básicos del return
                assertNotNull(result);
                assertEquals(adjustmentId, result.getAdjustmentId());

                // Capturamos lo que el servicio intentó persistir y validamos ahí
                var captor = org.mockito.ArgumentCaptor.forClass(Adjustment.class);
                verify(adjustmentRepository).save(captor.capture());
                Adjustment toSave = captor.getValue();

                // Validaciones sobre la cabecera
                assertEquals("139393", toSave.getDeductSerialNo());

                // Validaciones sobre el detalle actualizado
                assertNotNull(toSave.getDetail());
                assertEquals(2, toSave.getDetail().size());

                var detOpt = toSave.getDetail()
                                .stream()
                                .filter(d -> d.getId() != null && idRubro.equals(d.getId().getRubroId()))
                                .findFirst();

                assertTrue(detOpt.isPresent(), "No se encontró el detalle con rubroId=" + idRubro);
                var det = detOpt.get();

                assertEquals(new java.math.BigDecimal("338335"), det.getAdjustmentAmount());
                assertEquals("start", det.getStatus());

                verify(adjustmentRepository, times(1)).findByAdjustmentIdAndRubroId(adjustmentId, idRubro);
        }

        @Test
        void testUpdateAdjustment_NotFound() {
                // Arrange
                Long adjustmentId = 1L;
                Long idRubro = 133L;
                UpdateAdjustmentRequest updateAdjustmentRequest = new UpdateAdjustmentRequest();

                when(adjustmentRepository.findByAdjustmentIdAndRubroId(adjustmentId, idRubro))
                                .thenReturn(Optional.empty());

                // Act & Assert
                ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () -> {
                        adjustmentService.updateAdjustment(adjustmentId, idRubro, updateAdjustmentRequest);
                });

                // El servicio devuelve un mensaje extendido; validamos prefijo
                assertTrue(exception.getMessage() != null && exception.getMessage().startsWith("Adjustment not found"));
                verify(adjustmentRepository, times(1)).findByAdjustmentIdAndRubroId(adjustmentId, idRubro);
                verify(adjustmentRepository, never()).save(any(Adjustment.class));
        }

        //
        // ------- TESTS para findAdjustment(...) -------
        //
        @Test
        @DisplayName("findAdjustment: by adjustmentId only -> returns the record")
        void testFindAdjustment_ById_OK_noFilters() {
                Long adjustmentId = 10L;
                var adj = newAdj(adjustmentId, 3333L, 200L);

                when(adjustmentRepository.findByAdjustmentId(adjustmentId))
                                .thenReturn(Optional.of(adj));

                var out = adjustmentService.findAdjustment(null, null, "all", adjustmentId);

                assertTrue(out.isPresent());
                assertEquals(adjustmentId, out.get().getAdjustmentId());
                verify(adjustmentRepository).findByAdjustmentId(adjustmentId);
        }

        @Test
        @DisplayName("findAdjustment: by adjustmentId + invoiceId mismatch -> Optional.empty()")
        void testFindAdjustment_ById_WithInvoiceMismatch() {
                Long adjustmentId = 11L;
                var adj = newAdj(adjustmentId, 3333L, 300L);

                when(adjustmentRepository.findByAdjustmentId(adjustmentId))
                                .thenReturn(Optional.of(adj));

                var out = adjustmentService.findAdjustment(9999L, null, "all", adjustmentId);

                assertTrue(out.isEmpty());
                verify(adjustmentRepository).findByAdjustmentId(adjustmentId);
        }

        @Test
        @DisplayName("findAdjustment: by adjustmentId + caseId mismatch -> Optional.empty()")
        void testFindAdjustment_ById_WithCaseMismatch() {
                Long adjustmentId = 12L;
                var adj = newAdj(adjustmentId, 3333L, 999L);

                when(adjustmentRepository.findByAdjustmentId(adjustmentId))
                                .thenReturn(Optional.of(adj));

                var out = adjustmentService.findAdjustment(null, 1000L, "all", adjustmentId);

                assertTrue(out.isEmpty());
                verify(adjustmentRepository).findByAdjustmentId(adjustmentId);
        }

        @Test
        @DisplayName("findAdjustment: status='all' no afecta la consulta por id")
        void testFindAdjustment_ById_StatusAll_IgnoredForRepo() {
                Long adjustmentId = 13L;
                var adj = newAdj(adjustmentId, 3333L, 1L);

                when(adjustmentRepository.findByAdjustmentId(adjustmentId))
                                .thenReturn(Optional.of(adj));

                var out = adjustmentService.findAdjustment(3333L, 1L, "ALL", adjustmentId);

                assertTrue(out.isPresent());
                verify(adjustmentRepository).findByAdjustmentId(adjustmentId);
        }

        @Test
        @DisplayName("findAdjustment: latest by invoiceId + caseId -> repo devuelve Optional")
        void testFindAdjustment_Latest_ByInvoiceAndCase() {
                Long invoiceId = 3333L;
                Long caseId = 555L;
                var latest = newAdj(99L, invoiceId, caseId);

                when(adjustmentRepository.findFirstByInvoiceIdAndCaseIdOrderByAdjustmentIdDesc(invoiceId, caseId))
                                .thenReturn(Optional.of(latest));

                var out = adjustmentService.findAdjustment(invoiceId, caseId, "all", null);

                assertTrue(out.isPresent());
                assertEquals(99L, out.get().getAdjustmentId());
                verify(adjustmentRepository)
                                .findFirstByInvoiceIdAndCaseIdOrderByAdjustmentIdDesc(invoiceId, caseId);
        }

        @Test
        @DisplayName("findAdjustment: latest by invoiceId only -> repo devuelve Optional")
        void testFindAdjustment_Latest_ByInvoiceOnly() {
                Long invoiceId = 3333L;
                var a1 = newAdj(201L, invoiceId, 1L);

                when(adjustmentRepository.findFirstByInvoiceIdOrderByAdjustmentIdDesc(invoiceId))
                                .thenReturn(Optional.of(a1));

                var out = adjustmentService.findAdjustment(invoiceId, null, "", null);

                assertTrue(out.isPresent());
                assertEquals(201L, out.get().getAdjustmentId());
                verify(adjustmentRepository).findFirstByInvoiceIdOrderByAdjustmentIdDesc(invoiceId);
        }

        @Test
        @DisplayName("findAdjustment: latest by caseId only -> repo devuelve Optional")
        void testFindAdjustment_Latest_ByCaseOnly() {
                Long caseId = 8080L;
                var a1 = newAdj(300L, 3333L, caseId);

                when(adjustmentRepository.findFirstByCaseIdOrderByAdjustmentIdDesc(caseId))
                                .thenReturn(Optional.of(a1));

                var out = adjustmentService.findAdjustment(null, caseId, "all", null);

                assertTrue(out.isPresent());
                assertEquals(300L, out.get().getAdjustmentId());
                verify(adjustmentRepository).findFirstByCaseIdOrderByAdjustmentIdDesc(caseId);
        }

        @Test
        @DisplayName("findAdjustment: repo devuelve Optional.empty() -> Optional.empty()")
        void testFindAdjustment_Latest_Empty() {
                Long invoiceId = 3333L;

                when(adjustmentRepository.findFirstByInvoiceIdOrderByAdjustmentIdDesc(invoiceId))
                                .thenReturn(Optional.empty());

                var out = adjustmentService.findAdjustment(invoiceId, null, "all", null);

                assertTrue(out.isEmpty());
                verify(adjustmentRepository).findFirstByInvoiceIdOrderByAdjustmentIdDesc(invoiceId);
        }

        @Test
        @DisplayName("findAdjustment: no parameters -> Optional.empty() (controller ya valida 400)")
        void testFindAdjustment_NoParams() {
                Optional<Adjustment> out = adjustmentService.findAdjustment(
                                null, null, null, null);

                assertTrue(out.isEmpty());
                verifyNoInteractions(adjustmentRepository);
        }
}
