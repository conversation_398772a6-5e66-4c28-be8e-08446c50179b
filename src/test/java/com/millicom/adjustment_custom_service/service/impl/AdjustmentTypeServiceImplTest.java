package com.millicom.adjustment_custom_service.service.impl;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentTypeRequest;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeResponse;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;
import com.millicom.adjustment_custom_service.domain.repository.AdjustmentTypeRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class AdjustmentTypeServiceImplTest {

        @Mock
        private AdjustmentTypeRepository adjustmentTypeRepository;

        @InjectMocks
        private AdjustmentTypeServiceImpl adjustmentTypeService;

        @BeforeEach
        void setUp() {
                MockitoAnnotations.openMocks(this);
        }

        @Test
        void testCreateAdjustmentType_Success() {
                AdjustmentTypeRequest request = new AdjustmentTypeRequest();
                request.setDescription("Internet reconnection");
                request.setBillingCode("CC_ADJ_001");
                request.setType(AdjustmentType.AdjustmentKind.CREDIT);

                AdjustmentType saved = new AdjustmentType();
                saved.setId(123L);
                saved.setDescription("Internet reconnection");
                saved.setBillingCode("CC_ADJ_001");
                saved.setType(AdjustmentType.AdjustmentKind.CREDIT);

                when(adjustmentTypeRepository.existsByBillingCode("CC_ADJ_001")).thenReturn(false);
                when(adjustmentTypeRepository.save(any(AdjustmentType.class))).thenReturn(saved);

                AdjustmentType result = adjustmentTypeService.createAdjustmentType(request);

                assertNotNull(result);
                assertEquals(123L, result.getId());
                assertEquals("Internet reconnection", result.getDescription());
                assertEquals("CC_ADJ_001", result.getBillingCode());
                assertEquals(AdjustmentType.AdjustmentKind.CREDIT, result.getType());
                verify(adjustmentTypeRepository).existsByBillingCode("CC_ADJ_001");
                verify(adjustmentTypeRepository).save(any(AdjustmentType.class));
        }

        @Test
        void testCreateAdjustmentType_AlreadyExists() {
                AdjustmentTypeRequest request = new AdjustmentTypeRequest();
                request.setDescription("Duplicate");
                request.setBillingCode("CC_ADJ_DUP");
                request.setType(AdjustmentType.AdjustmentKind.DEBIT);

                when(adjustmentTypeRepository.existsByBillingCode("CC_ADJ_DUP")).thenReturn(true);

                ResponseStatusException ex = assertThrows(ResponseStatusException.class,
                                () -> adjustmentTypeService.createAdjustmentType(request));

                // Validar status y que el mensaje indique “exists”, sin acoplar al texto exacto
                assertEquals(409, ex.getStatusCode().value());
                assertNotNull(ex.getReason());
                assertTrue(ex.getReason().toLowerCase().contains("exist"),
                                "Reason should mention that it already exists, but was: " + ex.getReason());

                verify(adjustmentTypeRepository).existsByBillingCode("CC_ADJ_DUP");
                verify(adjustmentTypeRepository, never()).save(any(AdjustmentType.class));
        }

        @Test
        void testListAdjustmentTypes_WithData() {
                AdjustmentType credit = new AdjustmentType();
                credit.setId(1L);
                credit.setDescription("Sample credit");
                credit.setBillingCode("CC_ADJ_001");
                credit.setType(AdjustmentType.AdjustmentKind.CREDIT);

                AdjustmentType debit = new AdjustmentType();
                debit.setId(2L);
                debit.setDescription("Sample debit");
                debit.setBillingCode("CC_ADJ_002");
                debit.setType(AdjustmentType.AdjustmentKind.DEBIT);

                when(adjustmentTypeRepository.findAll()).thenReturn(List.of(credit, debit));

                List<AdjustmentTypeResponse> result = adjustmentTypeService.listAdjustmentTypes();

                assertNotNull(result);
                assertEquals(2, result.size());
                assertEquals(1L, result.get(0).getId());
                assertEquals(2L, result.get(1).getId());
                verify(adjustmentTypeRepository).findAll();
        }

        @Test
        void testListAdjustmentTypes_NoData() {
                when(adjustmentTypeRepository.findAll()).thenReturn(List.of());

                List<AdjustmentTypeResponse> result = adjustmentTypeService.listAdjustmentTypes();

                assertNotNull(result);
                assertTrue(result.isEmpty());
                verify(adjustmentTypeRepository).findAll();
        }
}
