package com.millicom.adjustment_custom_service.controller;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentTypeRequest;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeCatalogResponse;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeCreateResponse;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeResponse;
import com.millicom.adjustment_custom_service.service.AdjustmentTypeService;
import com.millicom.adjustment_custom_service.exception.model.ApiError;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;

@RestController
@RequestMapping("/api/v1/adjustments")
@AllArgsConstructor
public class AdjustmentTypeController {

        private final AdjustmentTypeService adjustmentTypeService;

        @Operation(summary = "Create a new adjustment type", description = "Creates a new adjustment type based on the provided request.")
        @ApiResponse(responseCode = "201", description = "Created", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AdjustmentTypeCreateResponse.class)))
        @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "403", description = "Forbidden / business error", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "404", description = "Not found", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "429", description = "Too many requests", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "409", description = "Conflict (already exists)", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "503", description = "Service unavailable", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "504", description = "Gateway timeout", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @PostMapping(value = "/catalog", consumes = "application/json", produces = "application/json")
        public ResponseEntity<AdjustmentTypeCreateResponse> createAdjustmentType(
                        @Valid @RequestBody AdjustmentTypeRequest request) {

                var created = adjustmentTypeService.createAdjustmentType(request);

                URI location = ServletUriComponentsBuilder
                                .fromCurrentRequest()
                                .path("/{id}")
                                .buildAndExpand(created.getId())
                                .toUri();

                return ResponseEntity.created(location)
                                .body(new AdjustmentTypeCreateResponse(created.getId()));

        }

        @Operation(summary = "List adjustment type", description = "List the types of adjustments.")
        @ApiResponse(responseCode = "200", description = "OK", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AdjustmentTypeCatalogResponse.class)))
        @ApiResponse(responseCode = "204", description = "No Content")
        @ApiResponse(responseCode = "400", description = "Bad Request", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "503", description = "Service unavailable", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "504", description = "Gateway timeout", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @GetMapping(value = "/catalog", produces = "application/json")
        public ResponseEntity<AdjustmentTypeCatalogResponse> listAdjustmentType() {
                List<AdjustmentTypeResponse> list = adjustmentTypeService.listAdjustmentTypes();
                if (list == null || list.isEmpty()) {
                        return ResponseEntity.noContent().build();
                }
                return ResponseEntity.ok(new AdjustmentTypeCatalogResponse(list));
        }

}
