package com.millicom.adjustment_custom_service.controller.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.ALWAYS)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response object for an adjustment detail")
public class AdjustmentDetailDTO {
    
    @Schema(description = "Parent adjustment id", example = "181")
    private Long adjustmentId;  

    @Schema(description = "Unique identifier for the rubro", example = "12345")
    private Long idRubro;

    @Schema(description = "Total adjustment amount", example = "1000.0000")
    private BigDecimal amount;

    @Schema(description = "Status of the adjustment", example = "init")
    private String status;

    @Schema(description = "Serial number for the dispute", example = "DISPUTE12345")
    private String disputeSerialNo;

    @Schema(description = "Serial number for the adjustment", example = "ADJUSTMENT67890")
    private String adjustmentSerialNo;
}
