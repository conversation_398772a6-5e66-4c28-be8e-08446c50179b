package com.millicom.adjustment_custom_service.controller.response;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.ALWAYS)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Response object for an adjustment")
public class AdjustmentResponseDTO {
    @Schema(description = "Unique identifier for the adjustment", example = "12345")
    private Long id;

    @Schema(description = "Invoice ID associated with the adjustment", example = "*********")
    private Long invoiceId;

    @Schema(description = "Account ID associated with the adjustment", example = "5000000")
    private String accountId;

    @Schema(description = "Total adjustment amount", example = "1000.0000")
    private BigDecimal totalAmount;

    @Schema(description = "Created By who created the adjustment", example = "john_doe")
    private String createdBy;

    @Schema(description = "Serial number for the deduction", example = "DED12345")
    private String deductSerialNo;

    @Schema(description = "Creation date of the adjustment", example = "2025-08-21T14:35:20.123")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime createdDate;

    @Schema(description = "User who approved the adjustment", example = "manager1")
    private String approvedBy;

    @Schema(description = "Date when the adjustment was approved", example = "2025-08-21T14:35:20.123")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime approvedDate;

    @Schema(description = "User who denied the adjustment", example = "auditor1")
    private String deniedBy;

    @Schema(description = "Date when the adjustment was denied", example = "2025-08-22T10:15:00.000")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime deniedDate;

    @Schema(description = "Case management ID associated with the adjustment", example = "12345")
    private Long caseId;

    @Schema(description = "List of adjustment details")
    private List<AdjustmentDetailDTO> detail;

    @Size(max = 64, message = "adjustmentTypeId must be less than or equal to 64 characters")
    @NotNull(message = "adjustmentTypeId cannot be null")
    private Long adjustmentTypeId;

    @Schema(description = "Type of the adjustment (CREDIT/DEBIT)", example = "CREDIT")
    private String type;

    @Schema(description = "Serial number for the dispute", example = "DISPUTE12345")
    private String disputeSerialNo;

    @Schema(description = "Charge code from debit adjustment type", example = "CHG001")
    private String chargeCode;

    @Schema(description = "disputeReason (CBS limite date)", example = "20370101000000")
    private String disputeReason;
}
