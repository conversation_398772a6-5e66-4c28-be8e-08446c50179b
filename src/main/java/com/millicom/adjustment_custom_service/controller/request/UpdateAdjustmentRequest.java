package com.millicom.adjustment_custom_service.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class UpdateAdjustmentRequest {

    @NotNull(message = "totalAmount cannot be null")
    @DecimalMin(value = "0.0000", message = "totalAmount must be non-negative")
    private BigDecimal totalAmount;

    @Size(max = 64, message = "deductSerialNo must be less than or equal to 64 characters")
    private String deductSerialNo;

    @Size(max = 255, message = "orderId must be less than or equal to 255 characters")
    private String orderId;

    @NotNull(message = "adjustmentTypeId cannot be null")
    @Positive(message = "adjustmentTypeId must be a positive number")
    private Long adjustmentTypeId;

    @Size(max = 100, message = "deniedBy must be less than or equal to 100 characters")
    private String deniedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime deniedDate;

    @Size(max = 100, message = "approvedBy must be less than or equal to 100 characters")
    private String approvedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime approvedDate;

    private List<UpdateAdjustmentDetailRequest> detail;
}
