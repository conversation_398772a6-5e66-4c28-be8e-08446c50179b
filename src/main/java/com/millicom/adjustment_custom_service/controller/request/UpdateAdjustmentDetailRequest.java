package com.millicom.adjustment_custom_service.controller.request;

import java.math.BigDecimal;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UpdateAdjustmentDetailRequest {

    @DecimalMin(value = "0.0000", message = "amount must be non-negative")
    private BigDecimal amount;

    @NotNull(message = "status cannot be null")
    @NotBlank(message = "status cannot be blank")
    @Size(max = 100, message = "status must be less than or equal to 100 characters")
    private String status;

    @Size(max = 32, message = "disputeSerialNo must be less than or equal to 32 characters")
    private String disputeSerialNo;

    @Size(max = 64, message = "adjustmentSerialNo must be less than or equal to 64 characters")
    private String adjustmentSerialNo;
}
