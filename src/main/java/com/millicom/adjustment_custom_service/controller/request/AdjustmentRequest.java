package com.millicom.adjustment_custom_service.controller.request;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;

@Getter
@Setter
public class AdjustmentRequest {

    @Schema(description = "Invoice ID associated with the adjustment", example = "*********")
    @NotNull(message = "invoiceId cannot be null")
    @Min(value = 1, message = "invoiceId must be greater than or equal to 1")
    @Positive(message = "invoiceId must be a positive number")
    private Long invoiceId;

    @NotNull(message = "accountId cannot be null")
    @NotBlank(message = "accountId cannot be blank")
    @Size(max = 255, message = "accountId must be less than or equal to 255 characters")
    private String accountId;

    @NotNull(message = "totalAmount cannot be null")
    @DecimalMin(value = "0.0000", message = "totalAmount must be non-negative")
    private BigDecimal totalAmount;

    @NotNull(message = "totalAmount cannot be null")
    @NotBlank(message = "totalAmount cannot be blank")
    @Size(max = 100, message = "Created By must be less than or equal to 100 characters")
    private String createdBy;

    @NotNull(message = "totalAmount cannot be null")
    private Long caseId;

    @Size(max = 255, message = "orderId must be less than or equal to 255 characters")
    private String orderId;

    @NotNull(message = "adjustmentTypeId cannot be null")
    @Positive(message = "adjustmentTypeId must be a positive number")
    private Long adjustmentTypeId;
    
    @NotEmpty(message = "detail cannot be empty")
    private List<AdjustmentDetailRequest> detail;
}