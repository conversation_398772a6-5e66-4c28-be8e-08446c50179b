package com.millicom.adjustment_custom_service.controller.request;

import java.math.BigDecimal;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AdjustmentDetailRequest {

    @NotNull(message = "idRubro cannot be null")
    private Long idRubro;

    @NotNull(message = "Adjustment amount cannot be null")
    @DecimalMin(value = "0.0000", message = "Adjustment amount must be non-negative")
    private BigDecimal amount;
}