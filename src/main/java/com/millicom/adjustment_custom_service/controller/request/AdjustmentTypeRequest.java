package com.millicom.adjustment_custom_service.controller.request;

import jakarta.validation.constraints.NotNull;

import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AdjustmentTypeRequest {

    @NotBlank(message = "description cannot be blank")
    @Size(max = 255, message = "description must be less than or equal to 255 characters")
    private String description;

    @NotBlank(message = "billingCode cannot be blank")
    @Size(max = 255, message = "billingCode must be less than or equal to 255 characters")
    private String billingCode;

    @NotNull(message = "type cannot be null")
    private AdjustmentType.AdjustmentKind type;
}
