package com.millicom.adjustment_custom_service.controller;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentRequest;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentDetailDTO;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentResponseDTO;
import com.millicom.adjustment_custom_service.controller.response.NewAdjustmentResponseDTO;
import com.millicom.adjustment_custom_service.domain.entity.Adjustment;
import com.millicom.adjustment_custom_service.service.AdjustmentService;
import com.millicom.adjustment_custom_service.exception.custom.BadRequestException;
import com.millicom.adjustment_custom_service.exception.model.ApiError;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import com.millicom.adjustment_custom_service.controller.request.UpdateAdjustmentRequest;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.millicom.workflow.spi.base.constant.Constants.CBS_LIMIT_DATE;

@RestController
@RequestMapping("/api/v1/adjustments")
@AllArgsConstructor
public class AdjustmentController {

        private AdjustmentService adjustmentService;

        @Operation(summary = "Create a new adjustment", description = "Creates a new adjustment based on the provided request.")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "201", description = "Adjustment created successfully", content = @Content(mediaType = "application/json", schema = @Schema(implementation = NewAdjustmentResponseDTO.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "403", description = "Forbidden / Business rule violation", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "404", description = "Not Found - adjustmentTypeId or related entity not found", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "409", description = "Conflict - adjustment already exists or duplicates detected", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "429", description = "Too Many Requests - rate limit exceeded", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "503", description = "Service unavailable", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "504", description = "Gateway timeout", content = @Content(schema = @Schema(implementation = ApiError.class)))
        })
        @PostMapping(consumes = "application/json", produces = "application/json")
        public ResponseEntity<NewAdjustmentResponseDTO> createAdjustment(
                        @RequestBody @Validated AdjustmentRequest adjustmentRequest) {

                Adjustment adjustment = adjustmentService.createAdjustment(adjustmentRequest);

                NewAdjustmentResponseDTO response = new NewAdjustmentResponseDTO();
                response.setAdjustmentId(adjustment.getAdjustmentId());

                // Retornar 201 Created
                return ResponseEntity
                                .status(HttpStatus.CREATED)
                                .body(response);
        }

        @Operation(summary = "Get latest adjustment by invoiceId/caseId or a specific adjustment by adjustmentId", description = """
                        Retrieves:
                        - The **specific** adjustment when `adjustmentId` is provided (optionally filtering its details by `status`).
                        - Otherwise, the **most recent** adjustment matching `invoiceId` and/or `caseId`.

                        Rules:
                        - Debes enviar **al menos uno** de: `adjustmentId`, `invoiceId` o `caseId`.
                        - `status` es opcional y filtra SOLO los *adjustment details*.
                        - Si `status` viene y no hay detalles que coincidan, se devuelve el ajuste con `adjustmentDetail` vacío.
                        - Si no hay coincidencias, se responde **204 No Content**.
                        - Si envías `adjustmentId` junto con otros params, **se prioriza `adjustmentId`**.
                        """)
        @ApiResponse(responseCode = "200", description = "Adjustment found", content = @Content(mediaType = "application/json", schema = @Schema(implementation = AdjustmentResponseDTO.class)))
        @ApiResponse(responseCode = "204", description = "No Content - no adjustment matches the provided filters")
        @ApiResponse(responseCode = "400", description = "Bad Request - provide at least one of 'adjustmentId', 'invoiceId' or 'caseId'", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "500", description = "Internal Server Error", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @ApiResponse(responseCode = "503", description = "Service Unavailable", content = @Content(schema = @Schema(implementation = ApiError.class)))
        @GetMapping
        public ResponseEntity<AdjustmentResponseDTO> getAdjustment(
                        @RequestParam(required = false) Long adjustmentId,
                        @RequestParam(required = false) Long invoiceId,
                        @RequestParam(required = false) Long caseId,
                        @RequestParam(required = false) String status) {

                if (adjustmentId == null && invoiceId == null && caseId == null) {
                        throw new BadRequestException("Debe enviar al menos 'adjustmentId', 'invoiceId' o 'caseId'.");
                }

                Optional<Adjustment> adjustment = adjustmentService.findAdjustment(invoiceId, caseId, status,
                                adjustmentId);

                if (adjustment.isEmpty()) {
                        return ResponseEntity.noContent().build();
                }
                return setAdjustmentDTO(adjustment.get(), status); // ⬅️ pásale status
        }

        @Operation(summary = "Update an existing adjustment", description = "Updates an existing adjustment based on the provided request. "
                        +
                        "Supports updating the adjustment header only, or header plus a specific detail " +
                        "(when 'idRubro' query parameter is provided).")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Adjustment updated successfully", content = @Content(mediaType = "application/json", schema = @Schema(implementation = NewAdjustmentResponseDTO.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "403", description = "Forbidden / Business rule violation", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "404", description = "Not Found - adjustmentId or detail (idRubro) not found", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "409", description = "Conflict - inconsistent update detected", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "429", description = "Too Many Requests - rate limit exceeded", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "503", description = "Service unavailable", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "504", description = "Gateway timeout", content = @Content(schema = @Schema(implementation = ApiError.class)))
        })
        @PatchMapping(value = "/{adjustmentId}", consumes = "application/json", produces = "application/json")
        public ResponseEntity<NewAdjustmentResponseDTO> updateAdjustment(
                        @PathVariable Long adjustmentId,
                        @RequestParam(required = false) Long idRubro,
                        @Valid @RequestBody UpdateAdjustmentRequest updateAdjustmentRequest) {

                if (idRubro != null) {
                        if (updateAdjustmentRequest.getDetail() == null
                                        || updateAdjustmentRequest.getDetail().isEmpty()) {
                                throw new BadRequestException(
                                                "El campo adjustmentDetail es requerido cuando idRubro está presente.");
                        }

                        if (updateAdjustmentRequest.getDetail().size() != 1) {
                                throw new BadRequestException(
                                                "Solo se puede definir un elemento en adjustmentDetail.");
                        }
                } else {
                        if (updateAdjustmentRequest.getDetail() != null
                                        && !updateAdjustmentRequest.getDetail().isEmpty()) {
                                throw new BadRequestException(
                                                "El campo idRubro es requerido en el query param cuando adjustmentDetail está presente en el body.");
                        }
                }

                Adjustment updatedAdjustment = adjustmentService.updateAdjustment(adjustmentId, idRubro,
                                updateAdjustmentRequest);

                NewAdjustmentResponseDTO response = new NewAdjustmentResponseDTO();
                response.setAdjustmentId(updatedAdjustment.getAdjustmentId());

                return ResponseEntity.ok(response);
        }

        @Operation(summary = "Get adjustment by invoice Id", description = "Retrieves an adjustment by invoice Id and optional status.")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Adjustment retrieved successfully", content = {
                                        @Content(mediaType = "application/json", schema = @Schema(implementation = AdjustmentResponseDTO.class)) }),
                        @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content(schema = @Schema(implementation = ApiError.class))),
                        @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content(schema = @Schema(implementation = ApiError.class)))
        })

        private ResponseEntity<AdjustmentResponseDTO> setAdjustmentDTO(Adjustment adj, String status) {
                String normalized = (status == null || status.isBlank() || status.equalsIgnoreCase("all"))
                                ? null
                                : status;
                AdjustmentResponseDTO response = new AdjustmentResponseDTO();
                response.setId(adj.getAdjustmentId());
                response.setInvoiceId(adj.getInvoiceId());
                response.setAccountId(adj.getAccountId());
                response.setTotalAmount(adj.getTotalAmount());
                response.setCreatedBy(adj.getCreatedBy());
                response.setCaseId(adj.getCaseId());
                response.setDeductSerialNo(adj.getDeductSerialNo());
                response.setCreatedDate(adj.getCreatedDate());
                response.setApprovedBy(adj.getApprovedBy());
                response.setApprovedDate(adj.getApprovedDate());
                response.setDeniedBy(adj.getDeniedBy());
                response.setDeniedDate(adj.getDeniedDate());
                response.setType(adj.getAdjustmentTypeId().getType().name());
                response.setAdjustmentTypeId(adj.getAdjustmentTypeId().getId());
                response.setChargeCode(adj.getAdjustmentTypeId().getBillingCode());

                // Filtrar details por status (si viene y no es "all")
                List<AdjustmentDetailDTO> detailDTOs = adj.getDetail() == null ? Collections.emptyList()
                                : adj.getDetail().stream()
                                                .filter(d -> normalized == null
                                                                || normalized.equalsIgnoreCase(d.getStatus()))
                                                .map(detail -> {
                                                        AdjustmentDetailDTO dto = new AdjustmentDetailDTO();
                                                        dto.setAdjustmentId(detail.getId().getAdjustmentId());
                                                        dto.setIdRubro(detail.getId().getRubroId());
                                                        dto.setAmount(detail.getAdjustmentAmount());
                                                        dto.setStatus(detail.getStatus());
                                                        dto.setDisputeSerialNo(detail.getDisputeSerialNo());
                                                        dto.setAdjustmentSerialNo(detail.getAdjustmentSerialNo());
                                                        return dto;
                                                })
                                                .toList();

                response.setDetail(detailDTOs);

                // disputeSerialNo del primer detail (después del filtro)
                if (!detailDTOs.isEmpty()) {
                        response.setDisputeSerialNo(detailDTOs.get(0).getDisputeSerialNo());
                }

                response.setDisputeReason(CBS_LIMIT_DATE);

                return ResponseEntity.ok(response);
        }

}
