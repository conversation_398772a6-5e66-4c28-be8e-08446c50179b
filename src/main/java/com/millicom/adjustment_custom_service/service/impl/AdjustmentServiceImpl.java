package com.millicom.adjustment_custom_service.service.impl;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentDetailRequest;
import com.millicom.adjustment_custom_service.controller.request.AdjustmentRequest;
import com.millicom.adjustment_custom_service.controller.request.UpdateAdjustmentRequest;
import com.millicom.adjustment_custom_service.controller.request.UpdateAdjustmentDetailRequest;
import com.millicom.adjustment_custom_service.domain.entity.Adjustment;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentDetail;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentDetailId;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;
import com.millicom.adjustment_custom_service.domain.repository.AdjustmentDetailRepository;
import com.millicom.adjustment_custom_service.domain.repository.AdjustmentRepository;
import com.millicom.adjustment_custom_service.domain.repository.AdjustmentTypeRepository;
import com.millicom.adjustment_custom_service.service.AdjustmentService;
import com.millicom.adjustment_custom_service.exception.custom.ResourceNotFoundException;
import static com.millicom.adjustment_custom_service.util.AdjustmentUtils.validateAgainst;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
@Slf4j
public class AdjustmentServiceImpl implements AdjustmentService {

    private AdjustmentRepository adjustmentRepository;
    private AdjustmentDetailRepository adjustmentDetailRepository;
    private AdjustmentTypeRepository adjustmentTypeRepository;

    @Override
    @Transactional
    public Adjustment createAdjustment(AdjustmentRequest adjustmentRequest) {
        // Mapear AdjustmentRequest a Adjustment
        Adjustment adjustment = new Adjustment();
        adjustment.setInvoiceId(adjustmentRequest.getInvoiceId());
        adjustment.setAccountId(adjustmentRequest.getAccountId());
        adjustment.setTotalAmount(adjustmentRequest.getTotalAmount());
        adjustment.setCreatedBy(adjustmentRequest.getCreatedBy());
        // Fecha actual de creación
        adjustment.setCreatedDate(LocalDateTime.now());
        adjustment.setCaseId(adjustmentRequest.getCaseId());
        adjustment.setOrderId(adjustmentRequest.getOrderId());

        // Obtener y asignar DebitAdjustmentType
        AdjustmentType adjustmentType = adjustmentTypeRepository
                .findById(adjustmentRequest.getAdjustmentTypeId())
                .orElseThrow(() -> new DataIntegrityViolationException("Invalid adjustmentTypeId"));
        adjustment.setAdjustmentTypeId(adjustmentType);

        // Guardar Adjustment
        adjustment = adjustmentRepository.save(adjustment);

        // Mapear y guardar AdjustmentDetail
        List<AdjustmentDetail> adjustmentDetails = new ArrayList<>();
        for (AdjustmentDetailRequest detailRequest : adjustmentRequest.getDetail()) {
            AdjustmentDetailId detailId = new AdjustmentDetailId(adjustment.getAdjustmentId(),
                    detailRequest.getIdRubro());
            AdjustmentDetail detail = new AdjustmentDetail();
            detail.setId(detailId);
            detail.setAdjustment(adjustment);
            detail.setAdjustmentAmount(detailRequest.getAmount());
            detail.setStatus("init");
            adjustmentDetails.add(detail);
        }
        adjustmentDetailRepository.saveAll(adjustmentDetails);

        // Crear y devolver el DTO de respuesta
        return adjustment;
    }

    @Override
    @Transactional
    public Adjustment updateAdjustment(Long adjustmentId, Long idRubro,
            UpdateAdjustmentRequest updateAdjustmentRequest) {

        Adjustment adjustment;

        if (idRubro != null) {
            // Busca el ajuste y su detalle específico
            adjustment = adjustmentRepository.findByAdjustmentIdAndRubroId(adjustmentId, idRubro)
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Adjustment not found with id " + adjustmentId + " and rubroId " + idRubro));
        } else {
            // Busca la cabecera con detalles (para evitar LazyInitializationException)
            adjustment = adjustmentRepository.findByAdjustmentId(adjustmentId)
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Adjustment not found with id " + adjustmentId));
        }

        // --- actualizar cabecera ---
        adjustment.setTotalAmount(updateAdjustmentRequest.getTotalAmount());
        adjustment.setDeductSerialNo(updateAdjustmentRequest.getDeductSerialNo());
        adjustment.setOrderId(updateAdjustmentRequest.getOrderId());

        adjustmentTypeRepository.findById(updateAdjustmentRequest.getAdjustmentTypeId())
                .ifPresent(adjustment::setAdjustmentTypeId);

        adjustment.setDeniedBy(updateAdjustmentRequest.getDeniedBy());
        adjustment.setDeniedDate(updateAdjustmentRequest.getDeniedDate());
        adjustment.setApprovedBy(updateAdjustmentRequest.getApprovedBy());
        adjustment.setApprovedDate(updateAdjustmentRequest.getApprovedDate());

        // --- actualizar detalle SOLO si viene idRubro ---
        if (idRubro != null && updateAdjustmentRequest.getDetail() != null &&
                !updateAdjustmentRequest.getDetail().isEmpty()) {

            AdjustmentDetail detailRequest = adjustment.getDetail().stream()
                    .filter(d -> d.getId().getRubroId().equals(idRubro))
                    .findFirst()
                    .orElseThrow(() -> new ResourceNotFoundException("Detail not found for rubroId " + idRubro));

            UpdateAdjustmentDetailRequest updateDetail = updateAdjustmentRequest.getDetail().get(0);

            if (updateDetail.getAmount() != null) {
                detailRequest.setAdjustmentAmount(updateDetail.getAmount());
            }
            if (updateDetail.getStatus() != null) {
                detailRequest.setStatus(updateDetail.getStatus());
            }
            if (updateDetail.getAdjustmentSerialNo() != null) {
                detailRequest.setAdjustmentSerialNo(updateDetail.getAdjustmentSerialNo());
            }
            if (updateDetail.getDisputeSerialNo() != null) {
                detailRequest.setDisputeSerialNo(updateDetail.getDisputeSerialNo());
            }
        }

        return adjustmentRepository.save(adjustment);
    }

    @Override
    public Optional<Adjustment> findAdjustment(Long invoiceId, Long caseId, String status, Long adjustmentId) {

        Optional<Adjustment> base;
        if (adjustmentId != null) {
            base = adjustmentRepository.findByAdjustmentId(adjustmentId);
            // valida contra invoiceId/caseId si vinieron
            return validateAgainst(base, invoiceId, caseId);
        }

        if (invoiceId != null && caseId != null) {
            base = adjustmentRepository.findFirstByInvoiceIdAndCaseIdOrderByAdjustmentIdDesc(invoiceId, caseId);
        } else if (invoiceId != null) {
            base = adjustmentRepository.findFirstByInvoiceIdOrderByAdjustmentIdDesc(invoiceId);
        } else if (caseId != null) {
            base = adjustmentRepository.findFirstByCaseIdOrderByAdjustmentIdDesc(caseId);
        } else {
            return Optional.empty(); // controller ya valida 400
        }

        // Nota: no filtramos la colección aquí (para no mutar la entidad administrada).
        // Haremos el filtro al construir el DTO.
        return base;
    }

}
