package com.millicom.adjustment_custom_service.service.impl;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentTypeRequest;
import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeResponse;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;
import com.millicom.adjustment_custom_service.domain.repository.AdjustmentTypeRepository;
import com.millicom.adjustment_custom_service.mapper.AdjustmentTypeMapper;
import com.millicom.adjustment_custom_service.service.AdjustmentTypeService;
import lombok.AllArgsConstructor;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

@Service
@AllArgsConstructor
public class AdjustmentTypeServiceImpl implements AdjustmentTypeService {

    private final AdjustmentTypeRepository adjustmentTypeRepository;

    @Override
    public AdjustmentType createAdjustmentType(AdjustmentTypeRequest request) {
        // Verificar si ya existe un billingCode igual
        if (adjustmentTypeRepository.existsByBillingCode(request.getBillingCode())) {
            throw new ResponseStatusException(
                    HttpStatus.CONFLICT,
                    "An adjustment type with the given billingCode already exists");
        }

        AdjustmentType entity = new AdjustmentType();
        entity.setDescription(request.getDescription());
        entity.setBillingCode(request.getBillingCode());
        entity.setType(request.getType());

        return adjustmentTypeRepository.save(entity);
    }

    @Override
    public List<AdjustmentTypeResponse> listAdjustmentTypes() {
        return adjustmentTypeRepository.findAll()
                .stream()
                .map(AdjustmentTypeMapper::toResponse)
                .toList();
    }

}
