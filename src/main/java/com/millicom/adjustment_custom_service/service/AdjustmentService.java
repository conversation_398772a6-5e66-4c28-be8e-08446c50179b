package com.millicom.adjustment_custom_service.service;

import com.millicom.adjustment_custom_service.controller.request.AdjustmentRequest;
import com.millicom.adjustment_custom_service.controller.request.UpdateAdjustmentRequest;
import com.millicom.adjustment_custom_service.domain.entity.Adjustment;

import java.util.Optional;

public interface AdjustmentService {
    Adjustment createAdjustment(AdjustmentRequest adjustmentRequest);

    Adjustment updateAdjustment(Long adjustmentId, Long idRubro, UpdateAdjustmentRequest updateAdjustmentRequest);

    Optional<Adjustment> findAdjustment(Long invoiceId, Long caseId, String status, Long adjustmentId);

}
