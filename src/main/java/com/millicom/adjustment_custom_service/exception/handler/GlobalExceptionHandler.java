package com.millicom.adjustment_custom_service.exception.handler;

import java.sql.SQLTransientConnectionException;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.transaction.CannotCreateTransactionException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ResponseStatusException;
import org.zalando.problem.Status;

import com.millicom.adjustment_custom_service.exception.custom.BadRequestException;
import com.millicom.adjustment_custom_service.exception.custom.ResourceNotFoundException;
import com.millicom.adjustment_custom_service.exception.custom.ServiceUnavailableException;
import com.millicom.adjustment_custom_service.exception.model.ApiError;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private Status z(HttpStatusCode sc) {
        try {
            return Status.valueOf(sc.value());
        } catch (IllegalArgumentException e) {
            return Status.INTERNAL_SERVER_ERROR;
        }
    }

    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<ApiError> handleBadRequest(BadRequestException ex) {
        ApiError apiError = new ApiError(Status.BAD_REQUEST, "Bad Request", List.of(ex.getMessage()));
        return ResponseEntity.badRequest().body(apiError);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ApiError> handleNotFound(ResourceNotFoundException ex) {
        ApiError apiError = new ApiError(Status.NOT_FOUND, "Resource Not Found", List.of(ex.getMessage()));
        return ResponseEntity.status(Status.NOT_FOUND.getStatusCode()).body(apiError);
    }

    @ExceptionHandler(ServiceUnavailableException.class)
    public ResponseEntity<ApiError> handleServiceUnavailable(ServiceUnavailableException ex) {
        ApiError apiError = new ApiError(Status.SERVICE_UNAVAILABLE, "Service Unavailable", List.of(ex.getMessage()));
        return ResponseEntity.status(Status.SERVICE_UNAVAILABLE.getStatusCode()).body(apiError);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiError> handleGenericException(Exception ex) {
        ApiError apiError = new ApiError(Status.INTERNAL_SERVER_ERROR, "Internal Server Error",
                List.of("An unexpected error has occurred. Please try again later."));
        return ResponseEntity.internalServerError().body(apiError);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiError> handleValidationException(MethodArgumentNotValidException ex) {
        List<String> errors = ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.toList());

        ApiError apiError = new ApiError(Status.BAD_REQUEST, "Invalid request parameters", errors);
        return ResponseEntity.badRequest().body(apiError);
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiError> handleDataIntegrityViolation(DataIntegrityViolationException ex) {
        String root = ex.getRootCause() != null ? ex.getRootCause().getMessage() : ex.getMessage();
        boolean duplicate = root != null && root.toLowerCase().contains("duplicate entry"); // MariaDB/MySQL 1062

        var st = duplicate ? Status.CONFLICT : Status.BAD_REQUEST;
        var title = st.getReasonPhrase();
        var detail = duplicate
                ? "Adjustment type already exists"
                : "A database constraint was violated. Please check the request data.";

        var body = new ApiError(st, title, List.of(detail));
        return ResponseEntity.status(st.getStatusCode()).body(body);
    }

    @ExceptionHandler({ CannotCreateTransactionException.class, DataAccessResourceFailureException.class,
            SQLTransientConnectionException.class })
    public ResponseEntity<ApiError> handleDatabaseConnectionException(Exception ex) {
        String message = "Database connection error. Please try again later.";

        // Extraer detalles si están disponibles
        String detailedMessage = ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage();

        ApiError apiError = new ApiError(Status.SERVICE_UNAVAILABLE, message, List.of(detailedMessage));
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(apiError);
    }

    @ExceptionHandler(ResponseStatusException.class)
    public ResponseEntity<ApiError> handleResponseStatus(ResponseStatusException ex) {
        var st = z(ex.getStatusCode());
        var reason = ex.getReason() != null ? ex.getReason() : st.getReasonPhrase();
        var body = new ApiError(st, st.getReasonPhrase(), List.of(reason));
        return ResponseEntity.status(ex.getStatusCode()).body(body);
    }

    @ExceptionHandler(org.springframework.http.converter.HttpMessageNotReadableException.class)
    public ResponseEntity<ApiError> handleNotReadable(HttpMessageNotReadableException ex) {
        var st = Status.BAD_REQUEST;
        var body = new ApiError(st, st.getReasonPhrase(), List.of("Malformed JSON request"));
        return ResponseEntity.badRequest().body(body);
    }

    @ExceptionHandler(jakarta.validation.ConstraintViolationException.class)
    public ResponseEntity<ApiError> handleConstraintViolation(jakarta.validation.ConstraintViolationException ex) {
        var st = Status.BAD_REQUEST;
        var errors = ex.getConstraintViolations().stream()
                .map(v -> v.getPropertyPath() + ": " + v.getMessage())
                .toList();
        var body = new ApiError(st, "Invalid request parameters", errors);
        return ResponseEntity.badRequest().body(body);
    }

}
