package com.millicom.adjustment_custom_service.exception.model;

import org.zalando.problem.Status;

import lombok.Getter;

import java.util.List;

@Getter
public class ApiError {

    private final List<String> errors;
    private final String message;
    private final Status status;

    public ApiError(Status status, String message, List<String> errors) {
        this.errors = errors;
        this.message = message;
        this.status = status;
    }
}
