package com.millicom.adjustment_custom_service.domain.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.hibernate.annotations.BatchSize;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity(name = "adjustments")
@Getter
@Setter
public class Adjustment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "adjustment_id")
    private Long adjustmentId;

    @Column(name = "invoice_id")
    @NotNull(message = "invoiceId cannot be null")
    private Long invoiceId;

    @Column(name = "account_id", length = 255)
    @Size(max = 255, message = "accountId must be less than or equal to 255 characters")
    private String accountId;

    @Column(name = "total_adjustment_amount", precision = 19, scale = 4)
    private BigDecimal totalAmount;

    @Column(name = "created_by", length = 100)
    @Size(max = 100, message = "Created by  must be less than or equal to 100 characters")
    private String createdBy;

    @Schema(description = "Date when the adjustment was created", example = "2025-08-20T12:45:30.123")
    @NotNull(message = "createdDate cannot be null")
    private LocalDateTime createdDate;

    @Column(name = "approved_by", length = 100)
    private String approvedBy;

    @Column(name = "approved_date")
    private LocalDateTime approvedDate;

    @Column(name = "denied_by", length = 100)
    private String deniedBy;

    @Column(name = "denied_date")
    private LocalDateTime deniedDate;

    @Column(name = "case_management_id")
    private Long caseId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "adjustment_type_id")
    @NotNull(message = "adjustmentTypeId cannot be null")
    private AdjustmentType adjustmentTypeId;

    @Column(name = "deduct_serial", length = 64)
    @Size(max = 64, message = "deductSerial must be less than or equal to 64 characters")
    private String deductSerialNo;

    @Column(name = "order_id", length = 255)
    @Size(max = 255, message = "orderId must be less than or equal to 255 characters")
    private String orderId;

    @OneToMany(mappedBy = "adjustment", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonManagedReference
    @BatchSize(size = 64)
    private List<AdjustmentDetail> detail;
}
