package com.millicom.adjustment_custom_service.domain.entity;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonBackReference;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity(name = "adjustment_details")
@Getter
@Setter
public class AdjustmentDetail {

    @EmbeddedId
    private AdjustmentDetailId id;

    @MapsId("adjustmentId")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "adjustment_id", nullable = false)
    @JsonBackReference
    @ToString.Exclude // evita recursión en logs
    private Adjustment adjustment;

    @Column(name = "adjustment_amount", precision = 19, scale = 4)
    private BigDecimal adjustmentAmount;

    @Column(name = "status", length = 100)
    private String status;

    @Column(name = "dispute_serial_no", length = 32)
    private String disputeSerialNo;

    @Column(name = "adjustment_serial_no", length = 64)
    private String adjustmentSerialNo;
}