package com.millicom.adjustment_custom_service.domain.entity;

import org.hibernate.annotations.BatchSize;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Entity(name = "adjustment_types")
@BatchSize(size = 64)
@Getter
@Setter
public class AdjustmentType {
    public enum AdjustmentKind {
        CREDIT, DEBIT
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "description", length = 255, nullable = true)
    @Size(max = 255)
    private String description;

    @Column(name = "billing_code", length = 255, nullable = true)
    @Size(max = 255)
    private String billingCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, length = 6)
    @NotNull
    private AdjustmentKind type;
}
