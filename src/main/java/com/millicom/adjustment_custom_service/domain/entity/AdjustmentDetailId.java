package com.millicom.adjustment_custom_service.domain.entity;

import java.io.Serializable;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.EqualsAndHashCode;

@Embeddable
@Getter @Setter
@NoArgsConstructor @AllArgsConstructor
@EqualsAndHashCode(of = {"adjustmentId", "rubroId"}) // <- clave para Hibernate
public class AdjustmentDetailId implements Serializable {
    private static final long serialVersionUID = 1L;

    @Column(name = "adjustment_id", nullable = false)
    private Long adjustmentId;

    @Column(name = "rubro_id", nullable = false)
    private Long rubroId;
}