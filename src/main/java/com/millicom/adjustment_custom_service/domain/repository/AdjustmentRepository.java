package com.millicom.adjustment_custom_service.domain.repository;

import com.millicom.adjustment_custom_service.domain.entity.Adjustment;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AdjustmentRepository extends JpaRepository<Adjustment, Long> {

  @Query("""
          SELECT a
          FROM adjustments a
          LEFT JOIN FETCH a.detail d
          WHERE a.adjustmentId = :adjustmentId
          AND (:rubroId IS NULL OR d.id.rubroId = :rubroId)
      """)
  Optional<Adjustment> findByAdjustmentIdAndRubroId(Long adjustmentId, Long rubroId);

  Optional<Adjustment> findByCaseId(Long caseId);

  // Trae por ID incluyendo detail y type (evita N+1)
  @EntityGraph(attributePaths = { "detail", "adjustmentTypeId" })
  Optional<Adjustment> findByAdjustmentId(Long adjustmentId);

  // “Latest” por invoiceId + caseId
  @EntityGraph(attributePaths = { "detail", "adjustmentTypeId" })
  Optional<Adjustment> findFirstByInvoiceIdAndCaseIdOrderByAdjustmentIdDesc(Long invoiceId, Long caseId);

  // “Latest” por invoiceId
  @EntityGraph(attributePaths = { "detail", "adjustmentTypeId" })
  Optional<Adjustment> findFirstByInvoiceIdOrderByAdjustmentIdDesc(Long invoiceId);

  // “Latest” por caseId
  @EntityGraph(attributePaths = { "detail", "adjustmentTypeId" })
  Optional<Adjustment> findFirstByCaseIdOrderByAdjustmentIdDesc(Long caseId);

}
