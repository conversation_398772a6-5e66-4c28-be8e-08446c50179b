package com.millicom.adjustment_custom_service.domain.repository;

import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AdjustmentTypeRepository extends JpaRepository<AdjustmentType, Long> {
    boolean existsByBillingCode(String billingCode);
}
