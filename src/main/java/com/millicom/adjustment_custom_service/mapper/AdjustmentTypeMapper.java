package com.millicom.adjustment_custom_service.mapper;

import com.millicom.adjustment_custom_service.controller.response.AdjustmentTypeResponse;
import com.millicom.adjustment_custom_service.domain.entity.AdjustmentType;

public class AdjustmentTypeMapper {

    public static AdjustmentTypeResponse toResponse(AdjustmentType entity) {
        return new AdjustmentTypeResponse(
            entity.getId(),
            entity.getDescription(),
            entity.getBillingCode(),
            entity.getType()
        );
    }
}
