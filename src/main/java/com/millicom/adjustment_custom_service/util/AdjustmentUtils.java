package com.millicom.adjustment_custom_service.util;

import com.millicom.adjustment_custom_service.domain.entity.Adjustment;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class AdjustmentUtils {

    private AdjustmentUtils() {
    }

    /** "" o "all" => null (no filtra detalles) */
    public static String normalizeStatus(String status) {
        return (status == null || status.isBlank() || "all".equalsIgnoreCase(status)) ? null : status;
    }

    /** Devuelve el primero o Optional.empty() */
    public static Optional<Adjustment> firstOrEmpty(List<Adjustment> list) {
        return list.isEmpty() ? Optional.empty() : Optional.of(list.get(0));
    }

    /**
     * Valida que el ajuste obtenido cumpla con invoiceId/caseId si fueron enviados
     */
    public static Optional<Adjustment> validateAgainst(Optional<Adjustment> opt, Long invoiceId, Long caseId) {
        if (opt.isEmpty())
            return Optional.empty();

        Adjustment adj = opt.get();

        if (invoiceId != null && !invoiceId.equals(adj.getInvoiceId())) {
            return Optional.empty();
        }
        if (caseId != null && !Objects.equals(caseId, adj.getCaseId())) {
            return Optional.empty();
        }
        return opt;
    }

}
