# General settings
changeLogFile=src/main/resources/db/changelog/changelog-master.yaml
outputChangeLogFile=src/main/resources/db/changelog/changelog-master.yaml

# Database connection details
driver=org.mariadb.jdbc.Driver
url=***********************************************
jakarta.persistence.jdbc.url=***********************************************
username=root
password=Soporte2025!
referenceUrl=hibernate:spring:com.millicom.adjustment_custom_service.domain.entity?dialect=org.hibernate.dialect.MariaDBDialect&hibernate.physical_naming_strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy&hibernate.implicit_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy

# Logging and verbosity
#verbose=true
#logging=debug