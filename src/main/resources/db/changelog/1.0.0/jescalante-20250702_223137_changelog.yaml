databaseChangeLog:
- changeSet:
    id: *************-1
    author: jescalante
    changes:
    - createTable:
        columns:
        - column:
            constraints:
              nullable: false
              primaryKey: true
              primaryKeyName: adjustment_detailsPK
            name: adjustment_id
            type: BIGINT
        - column:
            constraints:
              nullable: false
              primaryKey: true
              primaryKeyName: adjustment_detailsPK
            name: rubro_id
            type: BIGINT
        - column:
            name: adjustment_amount
            type: DECIMAL(19, 4)
        - column:
            name: adjustment_serial_no
            type: VARCHAR(64)
        - column:
            name: dispute_serial_no
            type: VARCHAR(32)
        - column:
            name: status
            type: VARCHAR(100)
        tableName: adjustment_details
- changeSet:
    id: *************-2
    author: jescalante
    changes:
    - createTable:
        columns:
        - column:
            autoIncrement: true
            constraints:
              nullable: false
              primaryKey: true
              primaryKeyName: adjustmentsPK
            name: adjustment_id
            type: BIGINT
        - column:
            name: account_id
            type: VARCHAR(255)
        - column:
            name: case_management_id
            type: BIGINT
        - column:
            name: deduct_serial
            type: VARCHAR(64)
        - column:
            name: end_date
            type: date
        - column:
            name: invoice_id
            type: VARCHAR(64)
        - column:
            name: order_id
            type: VARCHAR(255)
        - column:
            name: start_date
            type: date
        - column:
            name: total_adjustment_amount
            type: DECIMAL(19, 4)
        - column:
            name: type_adjustment
            type: VARCHAR(50)
        - column:
            name: user
            type: VARCHAR(100)
        - column:
            constraints:
              nullable: false
            name: type_debit_id
            type: VARCHAR(64)
        tableName: adjustments
- changeSet:
    id: *************-3
    author: jescalante
    changes:
    - createTable:
        columns:
        - column:
            constraints:
              nullable: false
              primaryKey: true
              primaryKeyName: debit_adjustment_typesPK
            name: type_debit_id
            type: VARCHAR(64)
        - column:
            name: description
            type: VARCHAR(255)
        tableName: debit_adjustment_types
- changeSet:
    id: *************-4
    author: jescalante
    changes:
    - addUniqueConstraint:
        columnNames: type_debit_id
        constraintName: UC_DEBIT_ADJUSTMENT_TYPESTYPE_DEBIT_ID_COL
        tableName: debit_adjustment_types
- changeSet:
    id: *************-5
    author: jescalante
    changes:
    - addForeignKeyConstraint:
        baseColumnNames: type_debit_id
        baseTableName: adjustments
        constraintName: FK9581eywx1sstfti4jmve03tus
        deferrable: false
        initiallyDeferred: false
        referencedColumnNames: type_debit_id
        referencedTableName: debit_adjustment_types
        validate: true
- changeSet:
    id: *************-6
    author: jescalante
    changes:
    - addForeignKeyConstraint:
        baseColumnNames: adjustment_id
        baseTableName: adjustment_details
        constraintName: FKjmc25dl1ldwc8ejipi7ydy3dk
        deferrable: false
        initiallyDeferred: false
        referencedColumnNames: adjustment_id
        referencedTableName: adjustments
        validate: true

