databaseChangeLog:
  # 01) Eliminar FK en adjustments (igual que lo tenías, con rollback simple)
  - changeSet:
      id: 20250825-01-drop-fk-adjustments
      author: dhluna
      preConditions:
        onFail: MARK_RAN         # Si no existe la tabla/columna, no rompas el pipeline
        onError: HALT
        nestedPreconditions:
          - tableExists:
              tableName: adjustments
          - columnExists:
              tableName: adjustments
              columnName: adjustment_type_id
          - tableExists:
              tableName: debit_adjustment_types
      changes:
        # --- Variante dinámica para MariaDB/MySQL: no requiere conocer el nombre de la FK ---
        - sql:
            dbms: mysql,mariadb
            splitStatements: true
            stripComments: true
            sql: |
              -- Descubrir el nombre de la FK que referencia debit_adjustment_types(type_debit_id)
              SET @fk_name := (
                SELECT kcu.CONSTRAINT_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                WHERE kcu.TABLE_SCHEMA = DATABASE()
                  AND kcu.TABLE_NAME = 'adjustments'
                  AND kcu.COLUMN_NAME = 'adjustment_type_id'
                  AND kcu.REFERENCED_TABLE_NAME = 'debit_adjustment_types'
                  AND kcu.REFERENCED_COLUMN_NAME = 'type_debit_id'
                LIMIT 1
              );

              -- Construir un statement inofensivo si no hay FK, o el DROP si existe
              SET @stmt := IF(
                @fk_name IS NULL,
                'SELECT 1',
                CONCAT('ALTER TABLE `adjustments` DROP FOREIGN KEY `', @fk_name, '`')
              );

              PREPARE s FROM @stmt;
              EXECUTE s;
              DEALLOCATE PREPARE s;

      rollback:
        - sql:
            dbms: mysql,mariadb
            splitStatements: true
            stripComments: true
            sql: |
              -- Asegurar las claves referenciadas antes de re-crear la FK
              INSERT INTO debit_adjustment_types (type_debit_id)
              SELECT DISTINCT a.adjustment_type_id
              FROM adjustments a
              WHERE a.adjustment_type_id IS NOT NULL
                AND NOT EXISTS (
                  SELECT 1
                  FROM debit_adjustment_types d
                  WHERE d.type_debit_id = a.adjustment_type_id
                );

              -- Crear nuevamente la FK con un nombre CONSISTENTE entre ambientes
              ALTER TABLE `adjustments`
                ADD CONSTRAINT `fk_adjustments_adjustment_type`
                FOREIGN KEY (`adjustment_type_id`)
                REFERENCES `debit_adjustment_types` (`type_debit_id`)
                ON UPDATE RESTRICT
                ON DELETE RESTRICT;


  # 02) Renombrar tabla y columna PK en adjustment_types (CORREGIDO)
  - changeSet:
      id: 20250825-02-rename-table-and-id
      author: dhluna
      preConditions:
        onFail: HALT
        onError: HALT
        nestedPreconditions:
          - tableExists:
              tableName: debit_adjustment_types
          - columnExists:
              tableName: debit_adjustment_types
              columnName: type_debit_id
          # Verifica que todos los IDs son numéricos antes de pasar a BIGINT
          - sqlCheck:
              expectedResult: "0"
              sql: |
                SELECT COUNT(*)
                FROM debit_adjustment_types
                WHERE type_debit_id NOT REGEXP '^[0-9]+$'
      changes:
        # 1) Renombrar tabla
        - renameTable:
            oldTableName: debit_adjustment_types
            newTableName: adjustment_types

        # 2) Renombrar columna (manteniendo tipo original por ahora)
        - renameColumn:
            tableName: adjustment_types
            oldColumnName: type_debit_id
            newColumnName: id
            columnDataType: VARCHAR(64)

        # 3) Quitar la PK ANTES de tocar AUTO_INCREMENT (clave para MariaDB 10.5)
        - dropPrimaryKey:
            tableName: adjustment_types

        # 4) Convertir a BIGINT
        - modifyDataType:
            tableName: adjustment_types
            columnName: id
            newDataType: BIGINT

        # 5) Volver a poner la PK
        - addPrimaryKey:
            tableName: adjustment_types
            columnNames: id
            constraintName: pk_adjustment_types_id

        # 6) Ahora sí, marcar AUTO_INCREMENT (ya es KEY)
        - addAutoIncrement:
            tableName: adjustment_types
            columnName: id
            columnDataType: BIGINT

      rollback:
        # 1) Quitar AUTO_INCREMENT (no hay “dropAutoIncrement” → usamos MODIFY)
        - sql:
            sql: |
              ALTER TABLE adjustment_types MODIFY id BIGINT;

        # 2) Quitar PK
        - dropPrimaryKey:
            tableName: adjustment_types

        # 3) Volver a VARCHAR(64)
        - modifyDataType:
            tableName: adjustment_types
            columnName: id
            newDataType: VARCHAR(64)

        # 4) Renombrar columna a su nombre original
        - renameColumn:
            tableName: adjustment_types
            oldColumnName: id
            newColumnName: type_debit_id
            columnDataType: VARCHAR(64)

        # 5) Renombrar tabla a su nombre original
        - renameTable:
            oldTableName: adjustment_types
            newTableName: debit_adjustment_types

        # 6) Volver a poner PK original
        - addPrimaryKey:
            tableName: debit_adjustment_types
            columnNames: type_debit_id
            constraintName: pk_debit_adjustment_types
