  # 03) Agregar columna type con constraint
databaseChangeLog:
  - changeSet:
      id: 20250825-03-add-type-col
      author: dhluna
      preConditions:
        onFail: HALT
        onError: HALT
        nestedPreconditions:
          - tableExists:
              tableName: adjustment_types
      changes:
        - addColumn:
            tableName: adjustment_types
            columns:
              - column:
                  name: type
                  type: ENUM('CREDIT','DEBIT')
                  constraints:
                    nullable: false
      rollback:
        - dropColumn:
            tableName: adjustment_types
            columnName: type
