databaseChangeLog:
  # 01) Renombrar columna FK (ya lo tenías)
  - changeSet:
      id: 20250825-01-rename-fk-col
      author: dhluna
      preConditions:
        onFail: MARK_RAN
        onError: HALT
        nestedPreconditions:
          - tableExists:
              tableName: adjustments
          - columnExists:
              tableName: adjustments
              columnName: type_debit_id
      changes:
        - renameColumn:
            tableName: adjustments
            oldColumnName: type_debit_id
            newColumnName: adjustment_type_id
            columnDataType: BIGINT
      rollback:
        - renameColumn:
            tableName: adjustments
            oldColumnName: adjustment_type_id
            newColumnName: type_debit_id
            columnDataType: VARCHAR(64)

  # 02) Crear de nuevo la FK + índice
  - changeSet:
      id: 20250825-02-add-fk-adjustments
      author: dhluna
      preConditions:
        onFail: HALT
        onError: HALT
        nestedPreconditions:
          - tableExists:
              tableName: adjustments
          - columnExists:
              tableName: adjustments
              columnName: adjustment_type_id
          - tableExists:
              tableName: adjustment_types
          - columnExists:
              tableName: adjustment_types
              columnName: id
      changes:
        - addForeignKeyConstraint:
            baseTableName: adjustments
            baseColumnNames: adjustment_type_id
            referencedTableName: adjustment_types
            referencedColumnNames: id
            constraintName: fk_adjustments_adjustment_type_id
            onDelete: RESTRICT
            onUpdate: RESTRICT
        - createIndex:
            tableName: adjustments
            indexName: idx_adjustments_adjustment_type_id
            columns:
              - column:
                  name: adjustment_type_id
      rollback:
        - dropForeignKeyConstraint:
            baseTableName: adjustments
            constraintName: fk_adjustments_adjustment_type_id
        - dropIndex:
            tableName: adjustments
            indexName: idx_adjustments_adjustment_type_id

  # 03) Cambiar columnas start_date y end_date a DATETIME(3)
  - changeSet:
      id: 20250825-03-alter-dates
      author: dhluna
      changes:
        - modifyDataType:
            tableName: adjustments
            columnName: start_date
            newDataType: DATETIME(3)
        - modifyDataType:
            tableName: adjustments
            columnName: end_date
            newDataType: DATETIME(3)
      rollback:
        - modifyDataType:
            tableName: adjustments
            columnName: start_date
            newDataType: DATE
        - modifyDataType:
            tableName: adjustments
            columnName: end_date
            newDataType: DATE

  # 03.5) Normalizar invoice_id antes de convertir a BIGINT
  - changeSet:
      id: 20250825-035-fix-invoice-id-data
      author: dhluna
      changes:
        - sql:
            dbms: mariadb
            sql: |
              UPDATE adjustments
              SET invoice_id = NULL
              WHERE invoice_id REGEXP '[^0-9]';
      rollback:
        - sql:
            dbms: mariadb
            sql: |
              -- No rollback exacto, solo ejemplo
              UPDATE adjustments
              SET invoice_id = 'UNKNOWN'
              WHERE invoice_id IS NULL;

  # 04) Cambiar invoice_id a BIGINT
  - changeSet:
      id: 20250825-04-alter-invoice-id
      author: dhluna
      changes:
        - modifyDataType:
            tableName: adjustments
            columnName: invoice_id
            newDataType: BIGINT
      rollback:
        - modifyDataType:
            tableName: adjustments
            columnName: invoice_id
            newDataType: VARCHAR(64)

  # 05) Renombrar user -> created_by
  - changeSet:
      id: 20250825-05-rename-user
      author: dhluna
      preConditions:
        onFail: MARK_RAN
        nestedPreconditions:
          - columnExists:
              tableName: adjustments
              columnName: user
      changes:
        - renameColumn:
            tableName: adjustments
            oldColumnName: user
            newColumnName: created_by
            columnDataType: VARCHAR(100)
      rollback:
        - renameColumn:
            tableName: adjustments
            oldColumnName: created_by
            newColumnName: user
            columnDataType: VARCHAR(100)

  # 06) Agregar created_date (idempotente)
  - changeSet:
      id: 20250825-06-add-created-date
      author: dhluna
      runOnChange: true
      changes:
        - sql:
            splitStatements: true
            endDelimiter: ;
            sql: |
              -- 1) Agregar la columna si no existe (NULL primero para evitar caída)
              ALTER TABLE adjustments
                ADD COLUMN IF NOT EXISTS created_date DATETIME(3) NULL;

              -- 2) Backfill para filas existentes
              UPDATE adjustments
                SET created_date = CURRENT_TIMESTAMP(3)
              WHERE created_date IS NULL
                  OR created_date IN ('0000-00-00 00:00:00', '0000-00-00 00:00:00.000');

              -- 3) Dejarla NOT NULL + DEFAULT para inserts futuros
              ALTER TABLE adjustments
                MODIFY COLUMN created_date DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);
      rollback:
        - sql: |
            ALTER TABLE adjustments DROP COLUMN created_date;


  # 07) Eliminar type_adjustment
  - changeSet:
      id: 20250825-07-drop-type-adjustment
      author: dhluna
      preConditions:
        onFail: MARK_RAN
        nestedPreconditions:
          - columnExists:
              tableName: adjustments
              columnName: type_adjustment
      changes:
        - dropColumn:
            tableName: adjustments
            columnName: type_adjustment
      rollback:
        - addColumn:
            tableName: adjustments
            columns:
              - column:
                  name: type_adjustment
                  type: VARCHAR(50)

  # 08) Agregar campos de aprobación
  - changeSet:
      id: 20250825-08-add-approval-fields
      author: dhluna
      changes:
        - addColumn:
            tableName: adjustments
            columns:
              - column:
                  name: approved_by
                  type: VARCHAR(100)
                  constraints:
                    nullable: true
              - column:
                  name: approved_date
                  type: DATETIME(3)
                  constraints:
                    nullable: true
              - column:
                  name: denied_by
                  type: VARCHAR(100)
                  constraints:
                    nullable: true
              - column:
                  name: denied_date
                  type: DATETIME(3)
                  constraints:
                    nullable: true
      rollback:
        - dropColumn:
            tableName: adjustments
            columnName: approved_by
        - dropColumn:
            tableName: adjustments
            columnName: approved_date
        - dropColumn:
            tableName: adjustments
            columnName: denied_by
        - dropColumn:
            tableName: adjustments
            columnName: denied_date

  # 09) Drop start_date y end_date
  - changeSet:
      id: 20250825-09-drop-start-end-date
      author: dhluna
      preConditions:
        onFail: MARK_RAN
        nestedPreconditions:
          - tableExists:
              tableName: adjustments
          - or:
              - columnExists:
                  tableName: adjustments
                  columnName: start_date
              - columnExists:
                  tableName: adjustments
                  columnName: end_date
      changes:
        - dropColumn:
            tableName: adjustments
            columnName: start_date
        - dropColumn:
            tableName: adjustments
            columnName: end_date
      rollback:
        - addColumn:
            tableName: adjustments
            columns:
              - column:
                  name: start_date
                  type: DATETIME(3)
                  constraints:
                    nullable: true
              - column:
                  name: end_date
                  type: DATETIME(3)
                  constraints:
                    nullable: true

