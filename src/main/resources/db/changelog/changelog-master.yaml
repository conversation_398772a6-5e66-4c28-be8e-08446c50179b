databaseChangeLog:
  - include:
      author: jescalante 
      file: "db/changelog/1.0.0/jescalante-20250702_223137_changelog.yaml"
      labels: initial
  - include:
      author: jescalante
      file: "db/changelog/1.0.0/jescalante-20250626_163024_insert_initial_data.yaml"
      labels: initial
  - include:
      author: cristianOrtiz
      file: "db/changelog/1.0.0/cristianOrtiz-20250711_174300_add_field_billing_code_in_debit_adjustment_types.yaml"
      labels: initial
  # Primero corremos los cambios de adjustment_types
  - include:
      file: db/changelog/1.0.0/dhluna-20250825_adjustment_types_step1.yaml
  - include:
      file: db/changelog/1.0.0/dhluna-20250825_adjustment_types_step2.yaml
      labels: custom
  # Luego los cambios en adjustments (dependen del anterior)
  - include:
      author: dhluna
      file: "db/changelog/1.0.0/dhluna-20250825_adjustments.yaml"
      labels: custom

