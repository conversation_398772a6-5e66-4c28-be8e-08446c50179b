<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.0</version>
		<relativePath/>
		<!-- lookup parent from repository -->
	</parent>

	<groupId>com.millicom.adjustment.custom</groupId>
	<artifactId>adjustment-custom-service</artifactId>
	<version>1.0.0-SNAPSHOT</version>
	<name>${project.artifactId}</name>

	<properties>
		<java.version>21</java.version>
		<maven.build.timestamp.format>yyyyMMdd_HHmmss</maven.build.timestamp.format>
		<docker.image.from>galaxion/tsf/images/java-21:latest</docker.image.from>
		<workflow-spi.version>1.0.0-SNAPSHOT</workflow-spi.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.tomcat.embed</groupId>
					<artifactId>tomcat-embed-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Agregar la nueva versión de tomcat-embed-core -->
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-core</artifactId>
			<version>11.0.8</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.liquibase</groupId>
			<artifactId>liquibase-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mariadb.jdbc</groupId>
			<artifactId>mariadb-java-client</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.liquibase.ext</groupId>
			<artifactId>liquibase-hibernate6</artifactId>
			<version>4.31.1</version>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.8.5</version>
		</dependency>
		<dependency>
			<groupId>org.zalando</groupId>
			<artifactId>problem-spring-web-starter</artifactId>
			<version>0.29.1</version>
		</dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>

		<!--================================================================
        = WORKFLOW-ENGINE DTO
        =================================================================-->
		<dependency>
			<groupId>millicom.tigo.workflowengine</groupId>
			<artifactId>workflow-spi-tigo-co</artifactId>
			<version>${workflow-spi.version}</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.liquibase</groupId>
				<artifactId>liquibase-maven-plugin</artifactId>
				<version>4.31.1</version>
				<configuration>
					<changeSetAuthor>${user.name}</changeSetAuthor>
					<diffChangeLogFile>src/main/resources/db/changelog/1.0.0/${user.name}-${maven.build.timestamp}_changelog.yaml</diffChangeLogFile>
					<propertyFile>${liquibase.propertyFile}</propertyFile>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<profiles>
						<profile>${spring.profiles.active}</profile>
					</profiles>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>

			<!-- Plugin para pruebas unitarias (ejecuta *Test.java) -->
			<plugin>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<includes>
						<include>**/*Test.java</include>
					</includes>
				</configuration>
			</plugin>

			<!-- Plugin para pruebas de integración (ejecuta *IT.java) -->
			<plugin>
				<artifactId>maven-failsafe-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>integration-test</goal>
							<goal>verify</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<includes>
						<include>**/*IT.java</include>
					</includes>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>jescalante</id>
			<properties>
				<liquibase.propertyFile>src/main/resources/liquibase-jescalante.properties</liquibase.propertyFile>
				<spring.profiles.active>jescalante</spring.profiles.active>
			</properties>
			<dependencies>
				<dependency>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-devtools</artifactId>
					<scope>runtime</scope>
					<optional>true</optional>
				</dependency>
			</dependencies>
		</profile>

		<profile>
			<id>default</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<liquibase.propertyFile>src/main/resources/liquibase.properties</liquibase.propertyFile>
			</properties>
		</profile>

		<profile>
			<id>gitlab-ci</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.jacoco</groupId>
						<artifactId>jacoco-maven-plugin</artifactId>
						<version>0.8.12</version>
						<executions>
							<execution>
								<goals>
									<goal>prepare-agent</goal>
								</goals>
							</execution>
							<execution>
								<id>report</id>
								<phase>test</phase>
								<goals>
									<goal>report</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>com.google.cloud.tools</groupId>
						<artifactId>jib-maven-plugin</artifactId>
						<version>3.4.5</version>
						<configuration>
							<from>
								<image>{auto}/${docker.image.from}</image>
							</from>
							<to>
								<image>{auto}</image>
								<tags>
									<tag>${project.version}</tag>
								</tags>
							</to>
							<extraDirectories>
								<paths>
									<path>
										<from>src/main/resources</from>
										<into>/liquibase/changelog</into>
									</path>
								</paths>
							</extraDirectories>
							<container>
								<user>tomcat</user>
							</container>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
