# Makefile para gestión rápida de comandos Maven con Liquibase y Spring Boot
# Uso: make <comando> [PARAMS]

# Configuración
PROFILE ?= $(shell whoami)  # Perfil por defecto = usuario del sistema
TAG ?=                     # Tag para rollback/tag (requerido en comandos específicos)

.PHONY: help diff update rollback history tag run run-debug

help:
	@echo "Comandos disponibles:"
	@echo "  diff          Generar archivo de cambios"
	@echo "  update        Aplicar cambios a la BD"
	@echo "  rollback      Revertir a un tag específico (ej: make rollback TAG=v1.0)"
	@echo "  history       Mostrar historial de cambios"
	@echo "  tag           Crear nuevo tag (ej: make tag TAG=release_1.1)"
	@echo "  run           Iniciar aplicación"
	@echo "  test          Ejecutar pruebas"
	@echo "  run-debug     Iniciar aplicación en modo debug"
	@echo ""
	@echo "Ejemplos:"
	@echo "  make diff"
	@echo "  make update"
	@echo "  make rollback TAG=before-payment-changes"
	@echo "  make tag TAG=release_1.2.0"

diff:
	mvn liquibase:diff -P$(PROFILE)

update:
	mvn liquibase:update -P$(PROFILE)

rollback:
ifndef TAG
	$(error TAG no definido. Uso: make rollback TAG=<nombre_tag>)
endif
	mvn liquibase:rollback -Dliquibase.rollbackTag=$(TAG) -P$(PROFILE)

history:
	mvn liquibase:history -P$(PROFILE)

tag:
ifndef TAG
	$(error TAG no definido. Uso: make tag TAG=<nombre_tag>)
endif
	mvn liquibase:tag -Dliquibase.tag=$(TAG) -P$(PROFILE)

run:
	mvn spring-boot:run -P$(PROFILE)

run-debug:
	mvn spring-boot:run -P$(PROFILE) -Ddebug=true

test:
	mvn test -P$(PROFILE)